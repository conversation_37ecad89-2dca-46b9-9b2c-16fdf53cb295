/*----------------------------------------*/
/*  19. process
/*----------------------------------------*/
.process {
    .container {
        padding-left: 33px;
        padding-right: 33px;
        @include respond(lg) {
            padding-left: 15px;
            padding-right: 15px;
        }
    }
    &__wrap {
        background-color: #0F0E1E;
        padding: 70px;
        border: 1px solid #1B1F2F;
        @include border-radius(10px);
        padding-top: 40px;
        @include respond(lg) {
            padding: 40px;
            padding-top: 10px;
        }
        @include respond(xs) {
            padding: 30px 20px;
            padding-top: 10px;
        }
        .process__title {
            max-width: 465px;
        }
    }
    &__ss {
        @include respond(lg) {
            max-width: 400px;
        }
    }
    &__title {
        .title {
            font-size: 32px;
            color: #F6F6F7;
            margin-bottom: 15px;
            @include respond(lg) {
                font-size: 28px;
            }
            @include respond(xs) {
                font-size: 26px;
            }
        }
        p {
            font-size: 16px;
            color: #8F9BB7;
        }
        &--lg {
            .title {
                font-size: 24px;
                margin-bottom: 10px;
            }
        }
    }
    &__app-item {
        padding: 50px 55px;
        background-color: #0F0E1E;
        border: 1px solid #1B1F2F;
        @include border-radius(10px);
        @include respond(xs) {
            padding: 30px 20px;
        }
        &.style-2 {
            padding-bottom: 0;
        }
        &.style-3 {
            padding: 30px;
            padding-bottom: 0;
            background-color: #030216;
            border-color: #282D45;
        }
    }
    &__button {
        border-radius: 96px;
        border: 1px solid #1B1F2F;
        background: linear-gradient(179deg, #0F0E1E 0%, rgba(15, 14, 30, 0.00) 100%);
        padding: 19px;
        @include respond(xs) {
            @include border-radius(10px);
        }
    }
    &__area {
        padding: 50px 54px;
        background-color: #0F0E1E;
        border: 1px solid #282D45;
        @include border-radius(10px);
        @include respond(xs) {
            padding: 30px 20px;
        }
    }
    &__top {
        .crm-title {
            max-width: 380px;
            &__heading {
                font-size: 48px;
                @include respond(xs) {
                    font-size: 30px;
                }
            }
        }
    }
    &__shape {
        .shape {
            position: absolute;
            z-index: -1;
            &--1 {
                left: 6%;
                top: 25%;
            }
            &--2 {
                left: 50%;
                @include transform(translateX(-50%));
                top: 5%;
                text-align: center;
                width: 100%;
            }
            &--3 {
                right: 5%;
                top: 44%;
            }
        }
    }
}
.g-38 {
    margin-left: -19px;
    margin-right: -19px;
    [class^="col-"] {
        padding: 0 19px;
    }
}
.g-26 {
    margin-left: -13px;
    margin-right: -13px;
    [class^="col-"] {
        padding: 0 13px;
    }
}