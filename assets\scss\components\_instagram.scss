/*----------------------------------------*/
/*  18. instagram
/*----------------------------------------*/
.instagram {
    &__wrap {
        margin-bottom: -152px;
        z-index: 2;
        position: relative;
    }
    &__item {
        @include border-radius(0 0 0 30px);
        overflow: hidden;
        @include transition(.3s);
        &.swiper-slide-active {
            @include border-radius(30px 0 0 0);
        }
    }
    &__content {
        background-color: #292B2F;
        padding: 55px 50px;
        min-height: 312px;
        @include border-radius(30px 0 0 0 );
        @include respond(lg) {
            padding: 20px;
            min-height: 100%;
        }
        @include respond(xs) {
            padding: 40px;
            padding-bottom: 60px;
        }
        a {
            font-size: 33px;
            margin-bottom: 40px;
            display: inline-block;
        }
        h3 {
            font-size: 20px;
            line-height: 30px;
            color: #fff;
            font-weight: 400;
            margin-bottom: 30px;
            @include respond(lg) {
                font-size: 19px;
                margin-bottom: 18px;
            }
        }
        .swiper-pagination {
            .swiper-pagination-bullet {
                width: 8px;
                height: 8px;
                background-color: #fff;
                &:not(:last-child) {
                    margin-right: 4px;
                }
            }
        }
    }
}
