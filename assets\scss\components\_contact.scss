/*----------------------------------------*/
/*  22. contact
/*----------------------------------------*/
.contact {
    padding-left: 60px;
    padding-right: 60px;
    @include respond(lg) {
        padding-left: 10px;
        padding-right: 10px;
    }
    @include respond(md) {
        padding-left: 0;
        padding-right: 0;
    }
    .row {
        margin: 0 -15px;
        & > * {
            padding: 0 15px;
        }
    }
    &__shape {
        .shape {
            position: absolute;
            &--1 {
                top: 42%;
                left: 2%;
            }
            &--2 {
                top: 36%;
                right: 0;
            }
        }
    }
}
.contact-info {
    &__item {
        text-align: center;
        border: 1px solid #E6EEFB;
        padding: 45px 20px;
        @include border-radius(10px);
        .icon {
            width: 58px;
            height: 58px;
            @include border-radius(50%);
            margin: 0 auto 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 1px solid #E6EEFB;
        }
        h3 {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 18px;
        }
        p {
            line-height: 30px;
        }
    }
}
.contact-form {
    .title {
        font-size: 30px;
        text-align: center;
        margin-bottom: 45px;
        @include respond(xs) {
            font-size: 26px;
            margin-bottom: 35px;
        }
    }
    input, textarea {
        height: 60px;
        background-color: #F9F9F9;
        padding: 20px;
        margin-bottom: 30px;
        @include border-radius(5px);
        border: 1px solid #EFF1F6;
    }
    textarea {
        min-height: 172px;
    }
}