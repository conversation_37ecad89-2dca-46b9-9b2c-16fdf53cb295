/*-----------------------------------------------------------------------------------

    Project Name: Cryco - ICO & Crypto Landing HTML Template
    Author: XpressBuddy -->> (https://themeforest.net/user/xpressbuddy)
    Support: <EMAIL>
    Description: Cryco - ICO & Crypto Landing HTML Template
    Developer: <PERSON> Was<PERSON> -> <EMAIL>
    Version: 1.0

-----------------------------------------------------------------------------------

    CSS INDEX
    ===================

	01. Theme default CSS
    02. header
    03. global
    04. hero
    05. about
    06. feature
    07. token
    08. roadmap
    09. team
    10. faq
    11. brand
    12. contact
    13. blog
    14. user option
    15. advantages
    16. solution
    17. pricing
    18. service
    19. product
    20. cta
    21. newslatter
    22. preloader
    23. backtotop
    24. breadcrumb
    25. sec-title
    

-----------------------------------------------------------------------------------*/
:root {
  --font-body: 'Outfit', sans-serif;
  --font-heading: 'Manrope', serif;
  --color-primary: #2DD98F;
  --color-heading: #fff;
  --xb-track-color: #16171B;
  --color-dark: #0F172A;
  --color-white: #fff;
  --color-black: #000;
  --color-default: #C1C7DE;
  --color-gray: #F8F8F8;
  --color-border: #EFF5F8;
  --color-blue: #4a21ef;
  --easing: cubic-bezier(0.67, 0.04, 0.3, 0.91); }

/* reset css start */
:root {
  scroll-behavior: auto; }

html {
  scroll-behavior: smooth; }

body {
  padding: 0;
  margin: 0;
  font-weight: 400;
  position: relative;
  font-family: var(--font-body);
  font-size: 16px;
  line-height: 28px;
  color: var(--color-default);
  background-color: #080B18;
  scrollbar-color: var(--color-primary) var(--xb-track-color);
  scrollbar-width: thin;
  word-wrap: break-word;
}

body::-webkit-scrollbar {
  width: 7px; }

body::-webkit-scrollbar-track {
  background: var(--xb-track-color);
  border-radius: 10px; }

body::-webkit-scrollbar-thumb {
  background: var(--color-primary);
  border-radius: 10px; }

img {
  max-width: 100%;
  height: auto; }

ul {
  margin: 0px;
  padding: 0px; }

button {
  cursor: pointer; }

*:focus {
  outline: none; }

button {
  border: none;
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s; }

button:focus {
  outline: none; }

a {
  text-decoration: none;
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s; }

a, h1, h2, h3, h4, h5, h6, strong, dt, th, .comment-meta .fn, .comment-reply-link, .no-comments {
  color: var(--color-white);
}
a:hover, .bypostauthor .comment-author.vcard .fn::after, .recent-posts .rp-title a:hover {
  color: var(--color-primary);
}

select {
  height: 55px;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  background-image: url(../img/select-arrow.png);
  background-position: calc(100% - 10px) 50%;
  background-repeat: no-repeat;
  padding-right: 20px;
  background-color: transparent;
  border: 0; }

input[type="text"], input[type="email"], input[type="url"], input[type="password"], input[type="search"], input[type="number"], input[type="tel"], input[type="range"], input[type="date"], input[type="month"], input[type="week"], input[type="time"], input[type="datetime"], input[type="datetime-local"], input[type="color"], textarea {
  width: 100%;
  height: 75px;
  border-radius: 0;
  background-color: #F3F4F5;
  padding: 24px 20px;
  border: 0;
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
  color: var(--color-white);
  font-weight: 500;
}
  input[type="text"]::-webkit-input-placeholder, input[type="password"]::-webkit-input-placeholder, input[type="email"]::-webkit-input-placeholder, input[type="tel"]::-webkit-input-placeholder, form select::-webkit-input-placeholder, textarea::-webkit-input-placeholder {
    color: #888686;
    opacity: 1; }
  input[type="text"]::-moz-placeholder, input[type="password"]::-moz-placeholder, input[type="email"]::-moz-placeholder, input[type="tel"]::-moz-placeholder, form select::-moz-placeholder, textarea::-moz-placeholder {
    color: #888686;
    opacity: 1; }
  input[type="text"]:-ms-input-placeholder, input[type="password"]:-ms-input-placeholder, input[type="email"]:-ms-input-placeholder, input[type="tel"]:-ms-input-placeholder, form select:-ms-input-placeholder, textarea:-ms-input-placeholder {
    color: #888686;
    opacity: 1; }
  input[type="text"]:-moz-placeholder, input[type="password"]:-moz-placeholder, input[type="email"]:-moz-placeholder, input[type="tel"]:-moz-placeholder, form select:-moz-placeholder, textarea:-moz-placeholder {
    color: #888686;
    opacity: 1; }

textarea {
  height: 100px; }

button {
  border: 0; }

table {
  width: 100%; }

p, li, span {
  margin-bottom: 0; }

/* reset css end */
/* global css start */
.nice-select {
  background-color: transparent;
  height: 75px;
  line-height: 73px;
  min-height: 70px;
  padding: 0 20px;
  width: 100%;
  padding: 0 46px;
  margin-bottom: 20px;
  background-color: #F3F4F5;
  border: 0;
  font-size: 16px; }
  .nice-select::after {
    border-bottom: 1.9px solid #B1B4BA;
    border-right: 1.9px solid #B1B4BA;
    height: 6px;
    right: 20px;
    width: 6px; }
  .nice-select .option:hover,
  .nice-select .option.focus,
  .nice-select .option.selected.focus {
    background-color: #F3F4F5; }
  .nice-select .list {
    width: 100%; }
    .nice-select .list li {
      margin-right: 0 !important; }
    .nice-select .list .option {
      color: var(--color-dark);
      font-weight: 500; }
      .nice-select .list .option.selected {
        font-weight: 600; }
      .nice-select .list .option.selected, .nice-select .list .option:hover {
        border: none !important; }

/* global css end */
.body_wrap {
  position: relative;
  overflow: hidden;
  background: #080b18; }

.body_wrap-two {
  position: relative;
  overflow: hidden;
  background: #4a21ef; }

.bg_img {
  background-position: center center;
  background-size: cover;
  background-repeat: no-repeat;
  width: 100%;
  height: 100%; }

.black-bg {
  background: var(--color-black); }

.gray-bg {
  background-color: var(--color-gray); }

.gray-bg-2 {
  background-color: var(--color-gray-2); }

.dark-bg {
  background-color: var(--color-dark); }

.white {
  color: var(--color-white); }

.pos-rel {
  position: relative; }

.pos-absolute {
  position: absolute; }

.f-right {
  float: right; }

.border-effect a, .border-effect-2 a {
  display: inline !important;
  width: 100%;
  background-repeat: no-repeat;
  background-position-y: -2px;
  background-image: linear-gradient(transparent calc(100% - 2px), currentColor 1px);
  -webkit-transition: 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
  -o-transition: 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
  transition: 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
  background-size: 0 100%;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden; }
  .border-effect a:hover, .border-effect-2 a:hover {
    background-size: 100% 100%;
    color: inherit; }

.border-effect-2 a {
  background-image: linear-gradient(transparent calc(100% - 1px), currentColor 1px); }
.site_wrapper {
  overflow: hidden;
  position: relative;
}
.btn-video {
  width: 88px;
  height: 88px;
  border-radius: 50%;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  -ms-border-radius: 50%;
  -o-border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 22px;
  color: var(--color-primary);
  position: relative;
  background-color: var(--color-white); }
  @media (max-width: 767px) {
    .btn-video {
      width: 70px;
      height: 70px;
      font-size: 18px; } }
  .btn-video:hover {
    color: var(--color-primary); }
  .btn-video::before {
    content: "";
    position: absolute;
    z-index: 0;
    left: 0;
    top: 0;
    display: block;
    width: 100%;
    height: 100%;
    background-color: var(--color-white);
    border-radius: 50%;
    animation: pulse-border 1500ms ease-out infinite;
    -webkit-animation: pulse-border 1500ms ease-out infinite;
    z-index: -2; }

.btn-video-center {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%); }

.border_effect a {
  display: inline !important;
  width: 100%;
  background-repeat: no-repeat;
  background-position-y: -2px;
  background-image: linear-gradient(transparent calc(100% - 2px), currentColor 1px);
  transition: 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
  background-size: 0 100%;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden; }
  .border_effect a:hover {
    background-size: 100% 100%;
    color: inherit; }

@media (max-width: 991px) {
  .tx-col-md-6 {
    width: 50%; } }
@media (max-width: 767px) {
  .tx-col-md-6 {
    width: 100%; } }

.xb-close {
  background: rgba(0, 0, 0, 0.04);
  border: 9px solid transparent;
  color: #777;
  width: 36px;
  height: 36px;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  position: relative; }
  .xb-close::before, .xb-close::after {
    content: '';
    position: absolute;
    height: 2px;
    width: 100%;
    top: 50%;
    left: 0;
    margin-top: -1px;
    transform-origin: 50% 50%;
    -webkit-transition: all 0.25s cubic-bezier(0.645, 0.045, 0.355, 1);
    -khtml-transition: all 0.25s cubic-bezier(0.645, 0.045, 0.355, 1);
    -moz-transition: all 0.25s cubic-bezier(0.645, 0.045, 0.355, 1);
    -ms-transition: all 0.25s cubic-bezier(0.645, 0.045, 0.355, 1);
    -o-transition: all 0.25s cubic-bezier(0.645, 0.045, 0.355, 1);
    transition: all 0.25s cubic-bezier(0.645, 0.045, 0.355, 1);
    background-color: #1b1b1b; }
  .xb-close::before {
    -webkit-transform: rotate(45deg);
    -khtml-transform: rotate(45deg);
    -moz-transform: rotate(45deg);
    -ms-transform: rotate(45deg);
    -o-transform: rotate(45deg);
    transform: rotate(45deg); }
  .xb-close::after {
    -webkit-transform: rotate(-45deg);
    -khtml-transform: rotate(-45deg);
    -moz-transform: rotate(-45deg);
    -ms-transform: rotate(-45deg);
    -o-transform: rotate(-45deg);
    transform: rotate(-45deg); }
  .xb-close:hover::before, .xb-close:hover::after {
    -webkit-transform: rotate(0);
    -khtml-transform: rotate(0);
    -moz-transform: rotate(0);
    -ms-transform: rotate(0);
    -o-transform: rotate(0);
    transform: rotate(0); }

/* order & unorder list reset - start */
.ul_li, .ul_li_right, .ul_li_center, .ul_li_between {
  margin: 0px;
  padding: 0px;
  display: flex;
  flex-wrap: wrap;
  align-items: center; }

.ul_li > li, .ul_li_right > li, .ul_li_center > li, .ul_li_between > li {
  float: left;
  list-style: none;
  display: inline-block; }

.ul_li {
  justify-content: flex-start; }

.ul_li_center {
  justify-content: center; }

.ul_li_right {
  justify-content: flex-end; }

.ul_li_between {
  justify-content: space-between; }

.ul_li_block {
  margin: 0px;
  padding: 0px;
  display: block; }

.ul_li_block > li {
  display: block;
  list-style: none; }

.flex-1 {
  flex: 1; }

.color-black {
  color: var(--color-black); }

.pagination_wrap {
  z-index: 2;
  position: relative; }
  .pagination_wrap ul {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    margin: -5px;
    list-style: none; }
    .pagination_wrap ul li {
      padding: 5px; }
      .pagination_wrap ul li a {
        height: 55px;
        width: 55px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 18px;
        font-weight: 500;
        color: var(--color-white);
        border: 1px solid #282842;
        -webkit-transition: all 0.3s ease-out 0s;
        -o-transition: all 0.3s ease-out 0s;
        transition: all 0.3s ease-out 0s;
        z-index: 1;
        border-radius: 50%;
        -webkit-border-radius: 50%;
        -moz-border-radius: 50%;
        -ms-border-radius: 50%;
        -o-border-radius: 50%;
        overflow: hidden; }
        .pagination_wrap ul li a.current_page, .pagination_wrap ul li a:hover {
          color: #282842;
          background-color: var(--color-primary);
          border-color: var(--color-primary); }

.mr-none-60 {
  margin-right: -60px; }

.ml-none-60 {
  margin-left: -60px; }

.pb-8 {
  padding-bottom: 8px; }

.xb-border::before {
  content: '';
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 1px;
  position: absolute;
  border-radius: 20px;
  -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: add, add;
  mask-composite: add, add;
  mask-composite: add, add;
  mask-composite: add, add;
  mask-composite: add, add;
  -webkit-mask-composite: source-out;
  mask-composite: exclude;
  background: linear-gradient(334deg, #6780d2 0%, #2f3b8d 100%);
  z-index: -1;
  opacity: .8; }

/*--
    - Margin & Padding
-----------------------------------------*/
/*-- Margin Top --*/
.mt-none-5 {
  margin-top: -5px; }

.mt-none-10 {
  margin-top: -10px; }

.mt-none-15 {
  margin-top: -15px; }

.mt-none-20 {
  margin-top: -20px; }

.mt-none-25 {
  margin-top: -25px; }

.mt-none-30 {
  margin-top: -30px; }

.mt-none-35 {
  margin-top: -35px; }

.mt-none-40 {
  margin-top: -40px; }

.mt-none-45 {
  margin-top: -45px; }

.mt-none-50 {
  margin-top: -50px; }

.mt-none-55 {
  margin-top: -55px; }

.mt-none-60 {
  margin-top: -60px; }

.mt-none-65 {
  margin-top: -65px; }

.mt-none-70 {
  margin-top: -70px; }

.mt-none-75 {
  margin-top: -75px; }

.mt-none-80 {
  margin-top: -80px; }

.mt-none-85 {
  margin-top: -85px; }

.mt-none-90 {
  margin-top: -90px; }

.mt-none-95 {
  margin-top: -95px; }

.mt-none-100 {
  margin-top: -100px; }

/*-- Margin Top --*/
.mt-5 {
  margin-top: 5px; }

.mt-10 {
  margin-top: 10px; }

.mt-15 {
  margin-top: 15px; }

.mt-20 {
  margin-top: 20px; }

.mt-25 {
  margin-top: 25px; }

.mt-30 {
  margin-top: 30px; }

.mt-35 {
  margin-top: 35px; }

.mt-40 {
  margin-top: 40px; }

.mt-45 {
  margin-top: 45px; }

.mt-50 {
  margin-top: 50px; }

.mt-55 {
  margin-top: 55px; }

.mt-60 {
  margin-top: 60px; }

.mt-65 {
  margin-top: 65px; }

.mt-70 {
  margin-top: 70px; }

.mt-75 {
  margin-top: 75px; }

.mt-80 {
  margin-top: 80px; }

.mt-85 {
  margin-top: 85px; }

.mt-90 {
  margin-top: 90px; }

.mt-95 {
  margin-top: 95px; }

.mt-100 {
  margin-top: 100px; }

.mt-105 {
  margin-top: 105px; }

.mt-110 {
  margin-top: 110px; }

.mt-115 {
  margin-top: 115px; }

.mt-120 {
  margin-top: 120px; }

.mt-125 {
  margin-top: 125px; }

.mt-130 {
  margin-top: 130px; }

.mt-135 {
  margin-top: 135px; }

.mt-140 {
  margin-top: 140px; }

.mt-145 {
  margin-top: 145px; }

.mt-150 {
  margin-top: 150px; }

.mt-155 {
  margin-top: 155px; }

.mt-160 {
  margin-top: 160px; }

.mt-165 {
  margin-top: 165px; }

.mt-170 {
  margin-top: 170px; }

.mt-175 {
  margin-top: 175px; }

.mt-180 {
  margin-top: 180px; }

.mt-185 {
  margin-top: 185px; }

.mt-190 {
  margin-top: 190px; }

.mt-195 {
  margin-top: 195px; }

.mt-200 {
  margin-top: 200px; }

/*-- Margin Bottom --*/
.mb-5 {
  margin-bottom: 5px; }

.mb-10 {
  margin-bottom: 10px; }

.mb-15 {
  margin-bottom: 15px; }

.mb-20 {
  margin-bottom: 20px; }

.mb-25 {
  margin-bottom: 25px; }

.mb-30 {
  margin-bottom: 30px; }

.mb-35 {
  margin-bottom: 35px; }

.mb-40 {
  margin-bottom: 40px; }

.mb-45 {
  margin-bottom: 45px; }

.mb-50 {
  margin-bottom: 50px; }

.mb-55 {
  margin-bottom: 55px; }

.mb-60 {
  margin-bottom: 60px; }

.mb-65 {
  margin-bottom: 65px; }

.mb-70 {
  margin-bottom: 70px; }

.mb-75 {
  margin-bottom: 75px; }

.mb-80 {
  margin-bottom: 80px; }

.mb-85 {
  margin-bottom: 85px; }

.mb-90 {
  margin-bottom: 90px; }

.mb-95 {
  margin-bottom: 95px; }

.mb-100 {
  margin-bottom: 100px; }

.mb-105 {
  margin-bottom: 105px; }

.mb-110 {
  margin-bottom: 110px; }

.mb-115 {
  margin-bottom: 115px; }

.mb-120 {
  margin-bottom: 120px; }

.mb-125 {
  margin-bottom: 125px; }

.mb-130 {
  margin-bottom: 130px; }

.mb-135 {
  margin-bottom: 135px; }

.mb-140 {
  margin-bottom: 140px; }

.mb-145 {
  margin-bottom: 145px; }

.mb-150 {
  margin-bottom: 150px; }

.mb-155 {
  margin-bottom: 155px; }

.mb-160 {
  margin-bottom: 160px; }

.mb-165 {
  margin-bottom: 165px; }

.mb-170 {
  margin-bottom: 170px; }

.mb-175 {
  margin-bottom: 175px; }

.mb-180 {
  margin-bottom: 180px; }

.mb-185 {
  margin-bottom: 185px; }

.mb-190 {
  margin-bottom: 190px; }

.mb-195 {
  margin-bottom: 195px; }

.mb-200 {
  margin-bottom: 200px; }

/*-- Margin Left --*/
.ml-5 {
  margin-left: 5px; }

.ml-10 {
  margin-left: 10px; }

.ml-15 {
  margin-left: 15px; }

.ml-20 {
  margin-left: 20px; }

.ml-25 {
  margin-left: 25px; }

.ml-30 {
  margin-left: 30px; }

.ml-35 {
  margin-left: 35px; }

.ml-40 {
  margin-left: 40px; }

.ml-45 {
  margin-left: 45px; }

.ml-50 {
  margin-left: 50px; }

.ml-55 {
  margin-left: 55px; }

.ml-60 {
  margin-left: 60px; }

.ml-65 {
  margin-left: 65px; }

.ml-70 {
  margin-left: 70px; }

.ml-75 {
  margin-left: 75px; }

.ml-80 {
  margin-left: 80px; }

.ml-85 {
  margin-left: 85px; }

.ml-90 {
  margin-left: 90px; }

.ml-95 {
  margin-left: 95px; }

.ml-100 {
  margin-left: 100px; }

.ml-105 {
  margin-left: 105px; }

.ml-110 {
  margin-left: 110px; }

.ml-115 {
  margin-left: 115px; }

.ml-120 {
  margin-left: 120px; }

.ml-125 {
  margin-left: 125px; }

.ml-130 {
  margin-left: 130px; }

.ml-135 {
  margin-left: 135px; }

.ml-140 {
  margin-left: 140px; }

.ml-145 {
  margin-left: 145px; }

.ml-150 {
  margin-left: 150px; }

.ml-155 {
  margin-left: 155px; }

.ml-160 {
  margin-left: 160px; }

.ml-165 {
  margin-left: 165px; }

.ml-170 {
  margin-left: 170px; }

.ml-175 {
  margin-left: 175px; }

.ml-180 {
  margin-left: 180px; }

.ml-185 {
  margin-left: 185px; }

.ml-190 {
  margin-left: 190px; }

.ml-195 {
  margin-left: 195px; }

.ml-200 {
  margin-left: 200px; }

/*-- Margin Right --*/
.mr-5 {
  margin-right: 5px; }

.mr-10 {
  margin-right: 10px; }

.mr-15 {
  margin-right: 15px; }

.mr-20 {
  margin-right: 20px; }

.mr-25 {
  margin-right: 25px; }

.mr-30 {
  margin-right: 30px; }

.mr-35 {
  margin-right: 35px; }

.mr-40 {
  margin-right: 40px; }

.mr-45 {
  margin-right: 45px; }

.mr-50 {
  margin-right: 50px; }

.mr-55 {
  margin-right: 55px; }

.mr-60 {
  margin-right: 60px; }

.mr-65 {
  margin-right: 65px; }

.mr-70 {
  margin-right: 70px; }

.mr-75 {
  margin-right: 75px; }

.mr-80 {
  margin-right: 80px; }

.mr-85 {
  margin-right: 85px; }

.mr-90 {
  margin-right: 90px; }

.mr-95 {
  margin-right: 95px; }

.mr-100 {
  margin-right: 100px; }

.mr-105 {
  margin-right: 105px; }

.mr-110 {
  margin-right: 110px; }

.mr-115 {
  margin-right: 115px; }

.mr-120 {
  margin-right: 120px; }

.mr-125 {
  margin-right: 125px; }

.mr-130 {
  margin-right: 130px; }

.mr-135 {
  margin-right: 135px; }

.mr-140 {
  margin-right: 140px; }

.mr-145 {
  margin-right: 145px; }

.mr-150 {
  margin-right: 150px; }

.mr-155 {
  margin-right: 155px; }

.mr-160 {
  margin-right: 160px; }

.mr-165 {
  margin-right: 165px; }

.mr-170 {
  margin-right: 170px; }

.mr-175 {
  margin-right: 175px; }

.mr-180 {
  margin-right: 180px; }

.mr-185 {
  margin-right: 185px; }

.mr-190 {
  margin-right: 190px; }

.mr-195 {
  margin-right: 195px; }

.mr-200 {
  margin-right: 200px; }

/*-- Padding Top --*/
.pt-5 {
  padding-top: 5px; }

.pt-10 {
  padding-top: 10px; }

.pt-15 {
  padding-top: 15px; }

.pt-20 {
  padding-top: 20px; }

.pt-25 {
  padding-top: 25px; }

.pt-30 {
  padding-top: 30px; }

.pt-35 {
  padding-top: 35px; }

.pt-40 {
  padding-top: 40px; }

.pt-45 {
  padding-top: 45px; }

.pt-50 {
  padding-top: 50px; }

.pt-55 {
  padding-top: 55px; }

.pt-60 {
  padding-top: 60px; }

.pt-65 {
  padding-top: 65px; }

.pt-70 {
  padding-top: 70px; }

.pt-75 {
  padding-top: 75px; }

.pt-80 {
  padding-top: 80px; }

.pt-85 {
  padding-top: 85px; }

.pt-90 {
  padding-top: 90px; }

.pt-95 {
  padding-top: 95px; }

.pt-100 {
  padding-top: 100px; }

.pt-105 {
  padding-top: 105px; }

.pt-110 {
  padding-top: 110px; }

.pt-115 {
  padding-top: 115px; }

.pt-120 {
  padding-top: 120px; }

.pt-125 {
  padding-top: 125px; }

.pt-130 {
  padding-top: 130px; }

.pt-135 {
  padding-top: 135px; }

.pt-140 {
  padding-top: 140px; }

.pt-145 {
  padding-top: 145px; }

.pt-150 {
  padding-top: 150px; }

.pt-155 {
  padding-top: 155px; }

.pt-160 {
  padding-top: 160px; }

.pt-165 {
  padding-top: 165px; }

.pt-170 {
  padding-top: 170px; }

.pt-175 {
  padding-top: 175px; }

.pt-180 {
  padding-top: 180px; }

.pt-185 {
  padding-top: 185px; }

.pt-190 {
  padding-top: 190px; }

.pt-195 {
  padding-top: 195px; }

.pt-200 {
  padding-top: 200px; }

/*-- Padding Bottom --*/
.pb-5 {
  padding-bottom: 5px; }

.pb-10 {
  padding-bottom: 10px; }

.pb-15 {
  padding-bottom: 15px; }

.pb-20 {
  padding-bottom: 20px; }

.pb-25 {
  padding-bottom: 25px; }

.pb-30 {
  padding-bottom: 30px; }

.pb-35 {
  padding-bottom: 35px; }

.pb-40 {
  padding-bottom: 40px; }

.pb-45 {
  padding-bottom: 45px; }

.pb-50 {
  padding-bottom: 50px; }

.pb-55 {
  padding-bottom: 55px; }

.pb-60 {
  padding-bottom: 60px; }

.pb-65 {
  padding-bottom: 65px; }

.pb-70 {
  padding-bottom: 70px; }

.pb-75 {
  padding-bottom: 75px; }

.pb-80 {
  padding-bottom: 80px; }

.pb-85 {
  padding-bottom: 85px; }

.pb-90 {
  padding-bottom: 90px; }

.pb-95 {
  padding-bottom: 95px; }

.pb-100 {
  padding-bottom: 100px; }

.pb-105 {
  padding-bottom: 105px; }

.pb-110 {
  padding-bottom: 110px; }

.pb-115 {
  padding-bottom: 115px; }

.pb-120 {
  padding-bottom: 120px; }

.pb-125 {
  padding-bottom: 125px; }

.pb-130 {
  padding-bottom: 130px; }

.pb-135 {
  padding-bottom: 135px; }

.pb-140 {
  padding-bottom: 140px; }

.pb-145 {
  padding-bottom: 145px; }

.pb-150 {
  padding-bottom: 150px; }

.pb-155 {
  padding-bottom: 155px; }

.pb-160 {
  padding-bottom: 160px; }

.pb-165 {
  padding-bottom: 165px; }

.pb-170 {
  padding-bottom: 170px; }

.pb-175 {
  padding-bottom: 175px; }

.pb-180 {
  padding-bottom: 180px; }

.pb-185 {
  padding-bottom: 185px; }

.pb-190 {
  padding-bottom: 190px; }

.pb-195 {
  padding-bottom: 195px; }

.pb-200 {
  padding-bottom: 200px; }

/*-- Padding Left --*/
.pl-5 {
  padding-left: 5px; }

.pl-10 {
  padding-left: 10px; }

.pl-15 {
  padding-left: 15px; }

.pl-20 {
  padding-left: 20px; }

.pl-25 {
  padding-left: 25px; }

.pl-30 {
  padding-left: 30px; }

.pl-35 {
  padding-left: 35px; }

.pl-40 {
  padding-left: 40px; }

.pl-45 {
  padding-left: 45px; }

.pl-50 {
  padding-left: 50px; }

.pl-55 {
  padding-left: 55px; }

.pl-60 {
  padding-left: 60px; }

.pl-65 {
  padding-left: 65px; }

.pl-70 {
  padding-left: 70px; }

.pl-75 {
  padding-left: 75px; }

.pl-80 {
  padding-left: 80px; }

.pl-85 {
  padding-left: 85px; }

.pl-90 {
  padding-left: 90px; }

.pl-95 {
  padding-left: 95px; }

.pl-100 {
  padding-left: 100px; }

.pl-105 {
  padding-left: 105px; }

.pl-110 {
  padding-left: 110px; }

.pl-115 {
  padding-left: 115px; }

.pl-120 {
  padding-left: 120px; }

.pl-125 {
  padding-left: 125px; }

.pl-130 {
  padding-left: 130px; }

.pl-135 {
  padding-left: 135px; }

.pl-140 {
  padding-left: 140px; }

.pl-145 {
  padding-left: 145px; }

.pl-150 {
  padding-left: 150px; }

.pl-155 {
  padding-left: 155px; }

.pl-160 {
  padding-left: 160px; }

.pl-165 {
  padding-left: 165px; }

.pl-170 {
  padding-left: 170px; }

.pl-175 {
  padding-left: 175px; }

.pl-180 {
  padding-left: 180px; }

.pl-185 {
  padding-left: 185px; }

.pl-190 {
  padding-left: 190px; }

.pl-195 {
  padding-left: 195px; }

.pl-200 {
  padding-left: 200px; }

/*-- Padding Right --*/
.pr-5 {
  padding-right: 5px; }

.pr-10 {
  padding-right: 10px; }

.pr-15 {
  padding-right: 15px; }

.pr-20 {
  padding-right: 20px; }

.pr-25 {
  padding-right: 25px; }

.pr-30 {
  padding-right: 30px; }

.pr-35 {
  padding-right: 35px; }

.pr-40 {
  padding-right: 40px; }

.pr-45 {
  padding-right: 45px; }

.pr-50 {
  padding-right: 50px; }

.pr-55 {
  padding-right: 55px; }

.pr-60 {
  padding-right: 60px; }

.pr-65 {
  padding-right: 65px; }

.pr-70 {
  padding-right: 70px; }

.pr-75 {
  padding-right: 75px; }

.pr-80 {
  padding-right: 80px; }

.pr-85 {
  padding-right: 85px; }

.pr-90 {
  padding-right: 90px; }

.pr-95 {
  padding-right: 95px; }

.pr-100 {
  padding-right: 100px; }

.pr-105 {
  padding-right: 105px; }

.pr-110 {
  padding-right: 110px; }

.pr-115 {
  padding-right: 115px; }

.pr-120 {
  padding-right: 120px; }

.pr-125 {
  padding-right: 125px; }

.pr-130 {
  padding-right: 130px; }

.pr-135 {
  padding-right: 135px; }

.pr-140 {
  padding-right: 140px; }

.pr-145 {
  padding-right: 145px; }

.pr-150 {
  padding-right: 150px; }

.pr-155 {
  padding-right: 155px; }

.pr-160 {
  padding-right: 160px; }

.pr-165 {
  padding-right: 165px; }

.pr-170 {
  padding-right: 170px; }

.pr-175 {
  padding-right: 175px; }

.pr-180 {
  padding-right: 180px; }

.pr-185 {
  padding-right: 185px; }

.pr-190 {
  padding-right: 190px; }

.pr-195 {
  padding-right: 195px; }

.pr-200 {
  padding-right: 200px; }
/* typography css start */
h1, h2, h3, h4, h5, h6 {
  margin: 0;
  line-height: -0.02;
  color: var(--color-heading);
  font-weight: 700;
  font-family: var(--font-heading); }

h2 {
  font-size: 32px; }

h3 {
  font-size: 22px; }

h4 {
  font-size: 20px; }

h5 {
  font-size: 18px; }

h6 {
  font-size: 16px; }

/* typography css end */
@-webkit-keyframes jump {
  0% {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0); }
  40% {
    -webkit-transform: translate3d(0, 50%, 0);
    transform: translate3d(0, 50%, 0); }
  100% {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0); } }
@keyframes jump {
  0% {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0); }
  40% {
    -webkit-transform: translate3d(0, 50%, 0);
    transform: translate3d(0, 50%, 0); }
  100% {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0); } }
@-webkit-keyframes rotated {
  0% {
    -webkit-transform: rotate(0);
    transform: rotate(0); }
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg); } }
@keyframes rotated {
  0% {
    -webkit-transform: rotate(0);
    transform: rotate(0); }
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg); } }
@-webkit-keyframes rotatedHalf {
  0% {
    -webkit-transform: rotate(0);
    transform: rotate(0); }
  50% {
    -webkit-transform: rotate(90deg);
    transform: rotate(90deg); }
  100% {
    -webkit-transform: rotate(0);
    transform: rotate(0); } }
@keyframes rotatedHalf {
  0% {
    -webkit-transform: rotate(0);
    transform: rotate(0); }
  50% {
    -webkit-transform: rotate(90deg);
    transform: rotate(90deg); }
  100% {
    -webkit-transform: rotate(0);
    transform: rotate(0); } }
@-webkit-keyframes rotatedHalfTwo {
  0% {
    -webkit-transform: rotate(-90deg);
    transform: rotate(-90deg); }
  100% {
    -webkit-transform: rotate(90deg);
    transform: rotate(90deg); } }
@keyframes rotatedHalfTwo {
  0% {
    -webkit-transform: rotate(-90deg);
    transform: rotate(-90deg); }
  100% {
    -webkit-transform: rotate(90deg);
    transform: rotate(90deg); } }
@-webkit-keyframes scale-upOne {
  0% {
    -webkit-transform: scale(1);
    transform: scale(1); }
  100% {
    -webkit-transform: scale(0.2);
    transform: scale(0.2); } }
@keyframes scale-upOne {
  0% {
    -webkit-transform: scale(1);
    transform: scale(1); }
  100% {
    -webkit-transform: scale(0.2);
    transform: scale(0.2); } }
@-webkit-keyframes scale-right {
  0% {
    -webkit-transform: translateX(-50%);
    transform: translateX(-50%); }
  50% {
    -webkit-transform: translateX(50%);
    transform: translateX(50%); }
  100% {
    -webkit-transform: translateX(-50%);
    transform: translateX(-50%); } }
@keyframes scale-right {
  0% {
    -webkit-transform: translateX(-50%);
    transform: translateX(-50%); }
  50% {
    -webkit-transform: translateX(50%);
    transform: translateX(50%); }
  100% {
    -webkit-transform: translateX(-50%);
    transform: translateX(-50%); } }
@-webkit-keyframes fade-in {
  0% {
    opacity: 0.7; }
  40% {
    opacity: 1; }
  100% {
    opacity: 0.7; } }
@keyframes fade-in {
  0% {
    opacity: 0.7; }
  40% {
    opacity: 1; }
  100% {
    opacity: 0.7; } }
@keyframes hvr-ripple-out {
  0% {
    top: 0;
    right: 0;
    bottom: 0;
    left: 0; }
  100% {
    top: -6px;
    right: -6px;
    bottom: -6px;
    left: -6px; } }
@keyframes hvr-ripple-out-two {
  0% {
    top: 0;
    right: 0;
    bottom: 0;
    left: 0; }
  100% {
    top: -18px;
    right: -18px;
    bottom: -18px;
    left: -18px;
    opacity: 0; } }
@-webkit-keyframes scale-up-one {
  0% {
    -webkit-transform: scale(1);
    transform: scale(1); }
  40% {
    -webkit-transform: scale(0.5);
    transform: scale(0.5); }
  100% {
    -webkit-transform: scale(1);
    transform: scale(1); } }
@keyframes scale-up-one {
  0% {
    -webkit-transform: scale(1);
    transform: scale(1); }
  40% {
    -webkit-transform: scale(0.5);
    transform: scale(0.5); }
  100% {
    -webkit-transform: scale(1);
    transform: scale(1); } }
@-webkit-keyframes scale-up-two {
  0% {
    -webkit-transform: scale(0.5);
    transform: scale(0.5); }
  40% {
    -webkit-transform: scale(0.8);
    transform: scale(0.8); }
  100% {
    -webkit-transform: scale(0.5);
    transform: scale(0.5); } }
@keyframes scale-up-two {
  0% {
    -webkit-transform: scale(0.5);
    transform: scale(0.5); }
  40% {
    -webkit-transform: scale(0.8);
    transform: scale(0.8); }
  100% {
    -webkit-transform: scale(0.5);
    transform: scale(0.5); } }
@-webkit-keyframes scale-up-three {
  0% {
    -webkit-transform: scale(0.7);
    transform: scale(0.7); }
  40% {
    -webkit-transform: scale(0.4);
    transform: scale(0.4); }
  100% {
    -webkit-transform: scale(0.7);
    transform: scale(0.7); } }
@keyframes scale-up-three {
  0% {
    -webkit-transform: scale(0.7);
    transform: scale(0.7); }
  40% {
    -webkit-transform: scale(0.4);
    transform: scale(0.4); }
  100% {
    -webkit-transform: scale(0.7);
    transform: scale(0.7); } }
@keyframes animationFramesOne {
  0% {
    transform: translate(0px, 0px) rotate(0deg);
    -webkit-transform: translate(0px, 0px) rotate(0deg);
    -moz-transform: translate(0px, 0px) rotate(0deg);
    -ms-transform: translate(0px, 0px) rotate(0deg);
    -o-transform: translate(0px, 0px) rotate(0deg); }
  20% {
    transform: translate(73px, -1px) rotate(36deg);
    -webkit-transform: translate(73px, -1px) rotate(36deg);
    -moz-transform: translate(73px, -1px) rotate(36deg);
    -ms-transform: translate(73px, -1px) rotate(36deg);
    -o-transform: translate(73px, -1px) rotate(36deg); }
  40% {
    transform: translate(141px, -20px) rotate(72deg);
    -webkit-transform: translate(141px, -20px) rotate(72deg);
    -moz-transform: translate(141px, -20px) rotate(72deg);
    -ms-transform: translate(141px, -20px) rotate(72deg);
    -o-transform: translate(141px, -20px) rotate(72deg); }
  60% {
    transform: translate(83px, -60px) rotate(108deg);
    -webkit-transform: translate(83px, -60px) rotate(108deg);
    -moz-transform: translate(83px, -60px) rotate(108deg);
    -ms-transform: translate(83px, -60px) rotate(108deg);
    -o-transform: translate(83px, -60px) rotate(108deg); }
  80% {
    transform: translate(-40px, 72px) rotate(144deg);
    -webkit-transform: translate(-40px, 72px) rotate(144deg);
    -moz-transform: translate(-40px, 72px) rotate(144deg);
    -ms-transform: translate(-40px, 72px) rotate(144deg);
    -o-transform: translate(-40px, 72px) rotate(144deg); }
  100% {
    transform: translate(0px, 0px) rotate(0deg);
    -webkit-transform: translate(0px, 0px) rotate(0deg);
    -moz-transform: translate(0px, 0px) rotate(0deg);
    -ms-transform: translate(0px, 0px) rotate(0deg);
    -o-transform: translate(0px, 0px) rotate(0deg); } }
@-webkit-keyframes animationFramesOne {
  0% {
    -webkit-transform: translate(0px, 0px) rotate(0deg); }
  20% {
    -webkit-transform: translate(73px, -1px) rotate(36deg); }
  40% {
    -webkit-transform: translate(141px, 72px) rotate(72deg); }
  60% {
    -webkit-transform: translate(83px, 122px) rotate(108deg); }
  80% {
    -webkit-transform: translate(-40px, 72px) rotate(144deg); }
  100% {
    -webkit-transform: translate(0px, 0px) rotate(0deg); } }
@keyframes animationFramesTwo {
  0% {
    transform: translate(0px, 0px) rotate(0deg) scale(1); }
  20% {
    transform: translate(73px, -1px) rotate(36deg) scale(0.9); }
  40% {
    transform: translate(141px, 72px) rotate(72deg) scale(1); }
  60% {
    transform: translate(83px, 122px) rotate(108deg) scale(1.2); }
  80% {
    transform: translate(-40px, 72px) rotate(144deg) scale(1.1); }
  100% {
    transform: translate(0px, 0px) rotate(0deg) scale(1); } }
@-webkit-keyframes animationFramesTwo {
  0% {
    -webkit-transform: translate(0px, 0px) rotate(0deg) scale(1); }
  20% {
    -webkit-transform: translate(73px, -1px) rotate(36deg) scale(0.9); }
  40% {
    -webkit-transform: translate(141px, 72px) rotate(72deg) scale(1); }
  60% {
    -webkit-transform: translate(83px, 122px) rotate(108deg) scale(1.2); }
  80% {
    -webkit-transform: translate(-40px, 72px) rotate(144deg) scale(1.1); }
  100% {
    -webkit-transform: translate(0px, 0px) rotate(0deg) scale(1); } }
@keyframes animationFramesThree {
  0% {
    transform: translate(165px, -30px);
    -webkit-transform: translate(165px, -30px);
    -moz-transform: translate(165px, -30px);
    -ms-transform: translate(165px, -30px);
    -o-transform: translate(165px, -30px); }
  100% {
    transform: translate(-60px, 80px);
    -webkit-transform: translate(-60px, 80px);
    -moz-transform: translate(-60px, 80px);
    -ms-transform: translate(-60px, 80px);
    -o-transform: translate(-60px, 80px); } }
@-webkit-keyframes animationFramesThree {
  0% {
    transform: translate(165px, -30px);
    -webkit-transform: translate(165px, -30px);
    -moz-transform: translate(165px, -30px);
    -ms-transform: translate(165px, -30px);
    -o-transform: translate(165px, -30px); }
  100% {
    transform: translate(-60px, 80px);
    -webkit-transform: translate(-60px, 80px);
    -moz-transform: translate(-60px, 80px);
    -ms-transform: translate(-60px, 80px);
    -o-transform: translate(-60px, 80px); } }
@keyframes animationFramesFour {
  0% {
    transform: translate(0px, 60px) rotate(0deg);
    -webkit-transform: translate(0px, 60px) rotate(0deg);
    -moz-transform: translate(0px, 60px) rotate(0deg);
    -ms-transform: translate(0px, 60px) rotate(0deg);
    -o-transform: translate(0px, 60px) rotate(0deg); }
  100% {
    transform: translate(-100px, -100px) rotate(180deg);
    -webkit-transform: translate(-100px, -100px) rotate(180deg);
    -moz-transform: translate(-100px, -100px) rotate(180deg);
    -ms-transform: translate(-100px, -100px) rotate(180deg);
    -o-transform: translate(-100px, -100px) rotate(180deg); } }
@-webkit-keyframes animationFramesFour {
  0% {
    transform: translate(0px, 60px) rotate(0deg);
    -webkit-transform: translate(0px, 60px) rotate(0deg);
    -moz-transform: translate(0px, 60px) rotate(0deg);
    -ms-transform: translate(0px, 60px) rotate(0deg);
    -o-transform: translate(0px, 60px) rotate(0deg); }
  100% {
    transform: translate(-100px, -100px) rotate(180deg);
    -webkit-transform: translate(-100px, -100px) rotate(180deg);
    -moz-transform: translate(-100px, -100px) rotate(180deg);
    -ms-transform: translate(-100px, -100px) rotate(180deg);
    -o-transform: translate(-100px, -100px) rotate(180deg); } }
@keyframes animationFramesFive {
  0% {
    transform: translate(0, 0) rotate(0deg);
    -webkit-transform: translate(0, 0) rotate(0deg);
    -moz-transform: translate(0, 0) rotate(0deg);
    -ms-transform: translate(0, 0) rotate(0deg);
    -o-transform: translate(0, 0) rotate(0deg); }
  21% {
    transform: translate(4px, -20px) rotate(38deg);
    -webkit-transform: translate(4px, -20px) rotate(38deg);
    -moz-transform: translate(4px, -20px) rotate(38deg);
    -ms-transform: translate(4px, -20px) rotate(38deg);
    -o-transform: translate(4px, -20px) rotate(38deg); }
  41% {
    transform: translate(-50px, -60px) rotate(74deg);
    -webkit-transform: translate(-50px, -60px) rotate(74deg);
    -moz-transform: translate(-50px, -60px) rotate(74deg);
    -ms-transform: translate(-50px, -60px) rotate(74deg);
    -o-transform: translate(-50px, -60px) rotate(74deg); }
  60% {
    transform: translate(-20px, -30px) rotate(108deg);
    -webkit-transform: translate(-20px, -30px) rotate(108deg);
    -moz-transform: translate(-20px, -30px) rotate(108deg);
    -ms-transform: translate(-20px, -30px) rotate(108deg);
    -o-transform: translate(-20px, -30px) rotate(108deg); }
  80% {
    transform: translate(-195px, -49px) rotate(144deg);
    -webkit-transform: translate(-195px, -49px) rotate(144deg);
    -moz-transform: translate(-195px, -49px) rotate(144deg);
    -ms-transform: translate(-195px, -49px) rotate(144deg);
    -o-transform: translate(-195px, -49px) rotate(144deg); }
  100% {
    transform: translate(-1px, 0px) rotate(180deg);
    -webkit-transform: translate(-1px, 0px) rotate(180deg);
    -moz-transform: translate(-1px, 0px) rotate(180deg);
    -ms-transform: translate(-1px, 0px) rotate(180deg);
    -o-transform: translate(-1px, 0px) rotate(180deg); } }
@-webkit-keyframes animationFramesFive {
  0% {
    transform: translate(0, 0) rotate(0deg);
    -webkit-transform: translate(0, 0) rotate(0deg);
    -moz-transform: translate(0, 0) rotate(0deg);
    -ms-transform: translate(0, 0) rotate(0deg);
    -o-transform: translate(0, 0) rotate(0deg); }
  21% {
    transform: translate(4px, -20px) rotate(38deg);
    -webkit-transform: translate(4px, -20px) rotate(38deg);
    -moz-transform: translate(4px, -20px) rotate(38deg);
    -ms-transform: translate(4px, -20px) rotate(38deg);
    -o-transform: translate(4px, -20px) rotate(38deg); }
  41% {
    transform: translate(-50px, -60px) rotate(74deg);
    -webkit-transform: translate(-50px, -60px) rotate(74deg);
    -moz-transform: translate(-50px, -60px) rotate(74deg);
    -ms-transform: translate(-50px, -60px) rotate(74deg);
    -o-transform: translate(-50px, -60px) rotate(74deg); }
  60% {
    transform: translate(-20px, -30px) rotate(108deg);
    -webkit-transform: translate(-20px, -30px) rotate(108deg);
    -moz-transform: translate(-20px, -30px) rotate(108deg);
    -ms-transform: translate(-20px, -30px) rotate(108deg);
    -o-transform: translate(-20px, -30px) rotate(108deg); }
  80% {
    transform: translate(-195px, -49px) rotate(144deg);
    -webkit-transform: translate(-195px, -49px) rotate(144deg);
    -moz-transform: translate(-195px, -49px) rotate(144deg);
    -ms-transform: translate(-195px, -49px) rotate(144deg);
    -o-transform: translate(-195px, -49px) rotate(144deg); }
  100% {
    transform: translate(-1px, 0px) rotate(180deg);
    -webkit-transform: translate(-1px, 0px) rotate(180deg);
    -moz-transform: translate(-1px, 0px) rotate(180deg);
    -ms-transform: translate(-1px, 0px) rotate(180deg);
    -o-transform: translate(-1px, 0px) rotate(180deg); } }
@keyframes spin {
  0% {
    transform: rotate(0deg); }
  100% {
    transform: rotate(360deg); } }
@-webkit-keyframes zoominup {
  0% {
    transform: scale(1.2); }
  50% {
    transform: scale(1); }
  100% {
    transform: scale(1.2); } }
@-webkit-keyframes updown {
  0% {
    transform: translateY(-10px);
    -webkit-transform: translateY(-10px);
    -moz-transform: translateY(-10px);
    -ms-transform: translateY(-10px);
    -o-transform: translateY(-10px); }
  50% {
    transform: translateY(-5px);
    -webkit-transform: translateY(-5px);
    -moz-transform: translateY(-5px);
    -ms-transform: translateY(-5px);
    -o-transform: translateY(-5px); }
  100% {
    transform: translateY(-10px);
    -webkit-transform: translateY(-10px);
    -moz-transform: translateY(-10px);
    -ms-transform: translateY(-10px);
    -o-transform: translateY(-10px); } }
@-webkit-keyframes updown-2 {
  0% {
    transform: translateY(-15px);
    -webkit-transform: translateY(-15px);
    -moz-transform: translateY(-15px);
    -ms-transform: translateY(-15px);
    -o-transform: translateY(-15px); }
  50% {
    transform: translateY(-5px);
    -webkit-transform: translateY(-5px);
    -moz-transform: translateY(-5px);
    -ms-transform: translateY(-5px);
    -o-transform: translateY(-5px); }
  100% {
    transform: translateY(-15px);
    -webkit-transform: translateY(-15px);
    -moz-transform: translateY(-15px);
    -ms-transform: translateY(-15px);
    -o-transform: translateY(-15px); } }
@keyframes ltr {
  0% {
    width: 0; }
  15% {
    width: 95%; }
  85% {
    opacity: 1; }
  90% {
    width: 95%;
    opacity: 0; }
  to {
    width: 0;
    opacity: 0; } }
/*circleAnimation*/
@-webkit-keyframes circleAnimation {
  0%,
    100% {
    border-radius: 42% 58% 70% 30% / 45% 45% 55% 55%;
    -webkit-transform: translate3d(0, 0, 0) rotateZ(0.01deg);
    transform: translate3d(0, 0, 0) rotateZ(0.01deg); }
  34% {
    border-radius: 70% 30% 46% 54% / 30% 29% 71% 70%;
    -webkit-transform: translate3d(0, 5px, 0) rotateZ(0.01deg);
    transform: translate3d(0, 5px, 0) rotateZ(0.01deg); }
  50% {
    -webkit-transform: translate3d(0, 0, 0) rotateZ(0.01deg);
    transform: translate3d(0, 0, 0) rotateZ(0.01deg); }
  67% {
    border-radius: 100% 60% 60% 100% / 100% 100% 60% 60%;
    -webkit-transform: translate3d(0, -3px, 0) rotateZ(0.01deg);
    transform: translate3d(0, -3px, 0) rotateZ(0.01deg); } }
@-webkit-keyframes icon-bounce {
  0%, 100%, 20%, 50%, 80% {
    -webkit-transform: translateY(0);
    transform: translateY(0); }
  40% {
    -webkit-transform: translateY(-10px);
    transform: translateY(-10px); }
  60% {
    -webkit-transform: translateY(-5px);
    transform: translateY(-5px); } }
@keyframes icon-bounce {
  0%, 100%, 20%, 50%, 80% {
    -webkit-transform: translateY(0);
    transform: translateY(0); }
  40% {
    -webkit-transform: translateY(-10px);
    transform: translateY(-10px); }
  60% {
    -webkit-transform: translateY(-5px);
    transform: translateY(-5px); } }
@keyframes lr-animation {
  0% {
    -webkit-transform: translateX(40px);
    -ms-transform: translateX(40px);
    transform: translateX(40px); }
  100% {
    -webkit-transform: translateX(0);
    -ms-transform: translateX(0);
    transform: translateX(0); } }
@keyframes tb-animation {
  0% {
    -webkit-transform: translateY(30px);
    -ms-transform: translateY(30px);
    transform: translateY(30px); }
  100% {
    -webkit-transform: translateY(0);
    -ms-transform: translateY(0);
    transform: translateY(0); } }
@keyframes xb_up_down {
  0% {
    transform: translateY(0); }
  100% {
    transform: translateY(-20px); } }
.slide-up-down {
  animation: xb_up_down 1s ease infinite alternate; }

@keyframes xb_ltr {
  0% {
    transform: translateY(0); }
  100% {
    transform: translateX(-20px); } }
.slide-ltr {
  animation: xb_ltr 1s ease infinite alternate; }

@-webkit-keyframes spin {
  0% {
    transform: rotate(0deg); }
  100% {
    transform: rotate(360deg); } }
@keyframes spin {
  0% {
    transform: rotate(0deg); }
  100% {
    transform: rotate(360deg); } }
@-webkit-keyframes zoom {
  0% {
    -webkit-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1); }
  50% {
    -webkit-transform: scale(1.05);
    -ms-transform: scale(1.05);
    transform: scale(1.05); }
  100% {
    -webkit-transform: scale(1.1);
    -ms-transform: scale(1.1);
    transform: scale(1.1); } }
@keyframes zoom {
  0% {
    -webkit-transform: scale(1.1);
    -ms-transform: scale(1.1);
    transform: scale(1.1); }
  50% {
    -webkit-transform: scale(1.05);
    -ms-transform: scale(1.05);
    transform: scale(1.05); }
  100% {
    -webkit-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1); } }
@-webkit-keyframes shake {
  0% {
    -webkit-transform: rotate(7deg);
    -ms-transform: rotate(7deg);
    transform: rotate(7deg); }
  50% {
    -webkit-transform: rotate(0deg);
    -ms-transform: rotate(0deg);
    transform: rotate(0deg); }
  100% {
    -webkit-transform: rotate(7deg);
    -ms-transform: rotate(7deg);
    transform: rotate(7deg); } }
@keyframes shake {
  0% {
    -webkit-transform: rotate(7deg);
    -ms-transform: rotate(7deg);
    transform: rotate(7deg); }
  50% {
    -webkit-transform: rotate(0deg);
    -ms-transform: rotate(0deg);
    transform: rotate(0deg); }
  100% {
    -webkit-transform: rotate(7deg);
    -ms-transform: rotate(7deg);
    transform: rotate(7deg); } }
@-webkit-keyframes down {
  0% {
    -webkit-transform: translateY(5px);
    -ms-transform: translateY(5px);
    transform: translateY(5px); }
  50% {
    -webkit-transform: translateY(0px);
    -ms-transform: translateY(0px);
    transform: translateY(0px); }
  100% {
    -webkit-transform: translateY(5px);
    -ms-transform: translateY(5px);
    transform: translateY(5px); } }
@keyframes down {
  0% {
    -webkit-transform: translateY(5px);
    -ms-transform: translateY(5px);
    transform: translateY(5px); }
  50% {
    -webkit-transform: translateY(0px);
    -ms-transform: translateY(0px);
    transform: translateY(0px); }
  100% {
    -webkit-transform: translateY(5px);
    -ms-transform: translateY(5px);
    transform: translateY(5px); } }
@keyframes outer-ripple {
  0% {
    transform: scale(1);
    filter: alpha(opacity=50);
    opacity: 0.5;
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    -o-transform: scale(1);
    -webkit-filter: alpha(opacity=50); }
  80% {
    transform: scale(1.5);
    filter: alpha(opacity=0);
    opacity: 0;
    -webkit-transform: scale(1.5);
    -moz-transform: scale(1.5);
    -ms-transform: scale(1.5);
    -o-transform: scale(1.5); }
  100% {
    transform: scale(2.5);
    filter: alpha(opacity=0);
    opacity: 0;
    -webkit-transform: scale(2.5);
    -moz-transform: scale(2.5);
    -ms-transform: scale(2.5);
    -o-transform: scale(2.5); } }
@-webkit-keyframes outer-ripple {
  0% {
    transform: scale(1);
    filter: alpha(opacity=50);
    opacity: 0.5;
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    -o-transform: scale(1); }
  80% {
    transform: scale(2.5);
    filter: alpha(opacity=0);
    opacity: 0;
    -webkit-transform: scale(2.5);
    -moz-transform: scale(2.5);
    -ms-transform: scale(2.5);
    -o-transform: scale(2.5); }
  100% {
    transform: scale(3.5);
    filter: alpha(opacity=0);
    opacity: 0;
    -webkit-transform: scale(3.5);
    -moz-transform: scale(3.5);
    -ms-transform: scale(3.5);
    -o-transform: scale(3.5); } }
@-moz-keyframes outer-ripple {
  0% {
    transform: scale(1);
    filter: alpha(opacity=50);
    opacity: 0.5;
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    -o-transform: scale(1); }
  80% {
    transform: scale(2.5);
    filter: alpha(opacity=0);
    opacity: 0;
    -webkit-transform: scale(2.5);
    -moz-transform: scale(2.5);
    -ms-transform: scale(2.5);
    -o-transform: scale(2.5); }
  100% {
    transform: scale(3.5);
    filter: alpha(opacity=0);
    opacity: 0;
    -webkit-transform: scale(3.5);
    -moz-transform: scale(3.5);
    -ms-transform: scale(3.5);
    -o-transform: scale(3.5); } }
@keyframes blink {
  from, to {
    opacity: 0; }
  50% {
    opacity: 1; } }
@-moz-keyframes blink {
  from, to {
    opacity: 0; }
  50% {
    opacity: 1; } }
@-webkit-keyframes blink {
  from, to {
    opacity: 0; }
  50% {
    opacity: 1; } }
@-ms-keyframes blink {
  from, to {
    opacity: 0; }
  50% {
    opacity: 1; } }
@-o-keyframes blink {
  from, to {
    opacity: 0; }
  50% {
    opacity: 1; } }
@keyframes pulse-border {
  0% {
    transform: scale(1);
    opacity: 0.67; }
  100% {
    transform: scale(1.8);
    opacity: 0; } }
@-webkit-keyframes pulse-border {
  0% {
    transform: scale(1);
    opacity: 0.67; }
  100% {
    transform: scale(1.8);
    opacity: 0; } }
@keyframes pulse-border-big {
  0% {
    transform: scale(1);
    opacity: 0.67; }
  100% {
    transform: scale(3);
    opacity: 0; } }
@-webkit-keyframes pulse-border-big {
  0% {
    transform: scale(1);
    opacity: 0.67; }
  100% {
    transform: scale(3);
    opacity: 0; } }
@-webkit-keyframes tada {
  0% {
    -webkit-transform: scale(1);
    transform: scale(1); }
  10%,
  20% {
    -webkit-transform: scale(0.9) rotate(-5deg);
    transform: scale(0.9) rotate(-5deg); }
  50%,
  50%,
  70%,
  90% {
    -webkit-transform: scale(1.1) rotate(5deg);
    transform: scale(1.1) rotate(5deg); }
  40%,
  60%,
  80% {
    -webkit-transform: scale(1.1) rotate(-5deg);
    transform: scale(1.1) rotate(-5deg); }
  100% {
    -webkit-transform: scale(1) rotate(0);
    transform: scale(1) rotate(0); } }
@keyframes tada {
  0% {
    -webkit-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1); }
  10%,
  20% {
    -webkit-transform: scale(0.9) rotate(-5deg);
    -ms-transform: scale(0.9) rotate(-5deg);
    transform: scale(0.9) rotate(-5deg); }
  50%,
  50%,
  70%,
  90% {
    -webkit-transform: scale(1.1) rotate(5deg);
    -ms-transform: scale(1.1) rotate(5deg);
    transform: scale(1.1) rotate(5deg); }
  40%,
  60%,
  80% {
    -webkit-transform: scale(1.1) rotate(-5deg);
    -ms-transform: scale(1.1) rotate(-5deg);
    transform: scale(1.1) rotate(-5deg); }
  100% {
    -webkit-transform: scale(1) rotate(0);
    -ms-transform: scale(1) rotate(0);
    transform: scale(1) rotate(0); } }
.wow.skewIn.animated {
  -webkit-animation-name: xbSkewIn;
  animation-name: xbSkewIn;
  -webkit-animation-fill-mode: both;
  animation-fill-mode: both;
  -webkit-animation-duration: .7s;
  animation-duration: .7s;
  -webkit-animation-timing-function: cubic-bezier(0.67, 0.04, 0.3, 0.91);
  animation-timing-function: cubic-bezier(0.67, 0.04, 0.3, 0.91);
  will-change: transform;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden; }

@-webkit-keyframes xbSkewIn {
  0% {
    -webkit-clip-path: inset(0 100% 0 0);
    clip-path: inset(0 100% 0 0);
    -webkit-transform: translateX(20px);
    transform: translateX(20px); }
  100% {
    -webkit-clip-path: inset(0 0 0 0);
    clip-path: inset(0 0 0 0);
    -webkit-transform: translateX(0);
    transform: translateX(0); } }
@keyframes xbSkewIn {
  0% {
    -webkit-clip-path: inset(0 100% 0 0);
    clip-path: inset(0 100% 0 0);
    -webkit-transform: translateX(20px);
    transform: translateX(20px); }
  100% {
    -webkit-clip-path: inset(0 0 0 0);
    clip-path: inset(0 0 0 0);
    -webkit-transform: translateX(0);
    transform: translateX(0); } }
.skewInImg {
  clip-path: polygon(0% 0%, 0% 0%, 0% 100%, 0% 100%);
  display: inline-block;
  position: relative;
  overflow: hidden;
  transition: 1.3s cubic-bezier(0.5, 0.5, 0, 1); }
  .skewInImg img {
    transform-origin: 50% 50%;
    transition: 1.3s cubic-bezier(0.5, 0.5, 0, 1);
    max-width: 100%;
    height: auto;
    transform: scale(1.5) translate(100px, 0px); }
  .skewInImg.animated {
    clip-path: polygon(0% 0%, 100% 0%, 100% 100%, 0% 100%); }
    .skewInImg.animated img {
      transform: scale(1) translate(0px, 0px); }

[data-aos=fade-up] {
  transform: translateY(50px); }

[data-aos=fade-down] {
  transform: translateY(-50px); }

[data-aos=fade-right] {
  transform: translate(-50px); }

[data-aos=fade-left] {
  transform: translate(50px); }

[data-aos=fade-up-right] {
  transform: translate(-50px, 50px); }

[data-aos=fade-up-left] {
  transform: translate(50px, 50px); }

[data-aos=fade-down-right] {
  transform: translate(-50px, -50px); }

[data-aos=fade-down-left] {
  transform: translate(50px, -50px); }

[data-aos][data-aos][data-aos-easing=ease],
body[data-aos-easing=ease] [data-aos] {
  transition-timing-function: cubic-bezier(0.18, 0.57, 0.25, 0.97); }

@-webkit-keyframes xb-danceTop {
  16% {
    -webkit-transform: skew(-14deg);
    transform: skew(-14deg); }
  33% {
    -webkit-transform: skew(12deg);
    transform: skew(12deg); }
  49% {
    -webkit-transform: skew(-8deg);
    transform: skew(-8deg); }
  66% {
    -webkit-transform: skew(6deg);
    transform: skew(6deg); }
  83% {
    -webkit-transform: skew(-4deg);
    transform: skew(-4deg); } }
@keyframes xb-danceTop {
  16% {
    -webkit-transform: skew(-14deg);
    transform: skew(-14deg); }
  33% {
    -webkit-transform: skew(12deg);
    transform: skew(12deg); }
  49% {
    -webkit-transform: skew(-8deg);
    transform: skew(-8deg); }
  66% {
    -webkit-transform: skew(6deg);
    transform: skew(6deg); }
  83% {
    -webkit-transform: skew(-4deg);
    transform: skew(-4deg); } }
@keyframes xbzoominzoomup {
  0% {
    transform: scale(0.8); }
  50% {
    transform: scale(1.2); }
  100% {
    transform: scale(0.8); } }
@keyframes rotateme {
  0% {
    transform: rotate(0deg);
    opacity: 1; }
  50% {
    transform: rotate(-180deg); }
  100% {
    transform: rotate(-360deg);
    opacity: 1; } }
@keyframes rotateme2 {
  0% {
    transform: rotate(0deg);
    opacity: 1; }
  50% {
    transform: rotate(180deg); }
  100% {
    transform: rotate(360deg);
    opacity: 1; } }
.rotateme {
  animation-name: rotateme;
  animation-duration: 15s;
  animation-iteration-count: infinite;
  animation-timing-function: linear; }

.rotateme2 {
  animation-name: rotateme2;
  animation-duration: 15s;
  animation-iteration-count: infinite;
  animation-timing-function: linear; }

@keyframes startFadeIn {
  0% {
    opacity: 1; }
  50% {
    opacity: 0.5; }
  100% {
    opacity: 0; } }
@keyframes startFadeInOut {
  0% {
    opacity: 1; }
  20% {
    opacity: 0.4; }
  40% {
    opacity: 0.6; }
  50% {
    opacity: 0.8; }
  60% {
    opacity: 0.6; }
  80% {
    opacity: 0.4; }
  100% {
    opacity: 1; } }
.move-animation-Y {
  animation: moveY 6s infinite cubic-bezier(0.4, 0, 1, 1); }

@keyframes moveY {
  0% {
    transform: translateY(0); }
  50% {
    transform: translateY(16px); }
  100% {
    transform: translateY(0); } }
.move-animation-6s-bounce {
  animation-duration: 6s;
  -webkit-animation-duration: 6s;
  animation-iteration-count: infinite;
  -webkit-animation-iteration-count: infinite;
  -webkit-animation-name: bounce;
  animation-name: bounce;
  -webkit-transform-origin: center bottom;
  transform-origin: center bottom; }

.move-animation-8s-bounce {
  animation-duration: 8s;
  -webkit-animation-duration: 8s;
  animation-iteration-count: infinite;
  -webkit-animation-iteration-count: infinite;
  -webkit-animation-name: bounce;
  animation-name: bounce;
  -webkit-transform-origin: center bottom;
  transform-origin: center bottom; }

@keyframes bounce {
  from,
  20%,
  53%,
  to {
    -webkit-animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0); }
  40%,
  43% {
    -webkit-animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
    animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
    -webkit-transform: translate3d(0, -30px, 0) scaleY(1.1);
    transform: translate3d(0, -30px, 0) scaleY(1.1); }
  70% {
    -webkit-animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
    animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
    -webkit-transform: translate3d(0, -15px, 0) scaleY(1.05);
    transform: translate3d(0, -15px, 0) scaleY(1.05); }
  80% {
    -webkit-transition-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    transition-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    -webkit-transform: translate3d(0, 0, 0) scaleY(0.95);
    transform: translate3d(0, 0, 0) scaleY(0.95); }
  90% {
    -webkit-transform: translate3d(0, -4px, 0) scaleY(1.02);
    transform: translate3d(0, -4px, 0) scaleY(1.02); } }
.move-backInDown {
  -webkit-animation-name: backInDown;
  animation-name: backInDown;
  animation-duration: 2s;
  -webkit-animation-duration: 2s; }

@-webkit-keyframes backInDown {
  0% {
    -webkit-transform: translateY(-1200px) scale(0.7);
    transform: translateY(-1200px) scale(0.7);
    opacity: 0.7; }
  80% {
    -webkit-transform: translateY(0px) scale(0.7);
    transform: translateY(0px) scale(0.7);
    opacity: 0.7; }
  100% {
    -webkit-transform: scale(1);
    transform: scale(1);
    opacity: 1; } }
@-webkit-keyframes bottomShadowBackInDown {
  0% {
    -webkit-transform: translateY(-1200px) scale(0.7);
    transform: translateY(-1200px) scale(0.7);
    opacity: 1; }
  20% {
    -webkit-transform: translateY(0px) scale(1);
    transform: translateY(0px) scale(1);
    opacity: 1; }
  100% {
    -webkit-transform: scale(1);
    transform: scale(1);
    opacity: 1; } }
@-webkit-keyframes lowerHalfBackInDown {
  0% {
    -webkit-transform: translateY(-1200px) scale(1);
    transform: translateY(-1200px) scale(1);
    opacity: 0.9; }
  25% {
    -webkit-transform: translateY(0px) scale(1);
    transform: translateY(0px) scale(1);
    opacity: 1; }
  100% {
    -webkit-transform: scale(1);
    transform: scale(1);
    opacity: 1; } }
@-webkit-keyframes tokenBackInDown {
  0% {
    -webkit-transform: translateY(-1200px) scale(0.7);
    transform: translateY(-1200px) scale(0.7);
    opacity: 1; }
  40% {
    -webkit-transform: translateY(0px) scale(1);
    transform: translateY(0px) scale(1);
    opacity: 1; } }
@keyframes leftToRight {
  0% {
    transform: rotateX(0deg) translateX(0px); }
  50% {
    transform: rotateX(0deg) translateX(40px); }
  100% {
    transform: rotateX(0deg) translateX(0px); } }
.leftToRight {
  -webkit-animation-name: leftToRight;
  animation-name: leftToRight;
  -webkit-animation-duration: 8s;
  animation-duration: 8s;
  -webkit-animation-iteration-count: infinite;
  animation-iteration-count: infinite;
  -webkit-animation-timing-function: linear;
  animation-timing-function: linear; }

@keyframes topToBottom {
  0% {
    transform: rotateX(0deg) translateY(0px); }
  50% {
    transform: rotateX(0deg) translateY(-30px); }
  100% {
    transform: rotateX(0deg) translateY(0px); } }
@keyframes topToBottom2 {
  0% {
    transform: rotateX(0deg) translateY(-30px); }
  50% {
    transform: rotateX(0deg) translateY(0); }
  100% {
    transform: rotateX(0deg) translateY(-30px); } }
.topToBottom {
  -webkit-animation-name: topToBottom;
  animation-name: topToBottom;
  -webkit-animation-duration: 6s;
  animation-duration: 6s;
  -webkit-animation-iteration-count: infinite;
  animation-iteration-count: infinite;
  -webkit-animation-timing-function: linear;
  animation-timing-function: linear; }
  .topToBottom.ad-8 {
    -webkit-animation-duration: 8s;
    animation-duration: 8s; }

.topToBottom2 {
  -webkit-animation-name: topToBottom2;
  animation-name: topToBottom2;
  -webkit-animation-duration: 6s;
  animation-duration: 6s;
  -webkit-animation-iteration-count: infinite;
  animation-iteration-count: infinite;
  -webkit-animation-timing-function: linear;
  animation-timing-function: linear; }
  .topToBottom2.ad-8 {
    -webkit-animation-duration: 8s;
    animation-duration: 8s; }

/*--
    - Overlay
------------------------------------------*/
[data-overlay] {
  position: relative;
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center center; }
  [data-overlay]::before {
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    content: "";
    width: 100%;
    height: 100%; }

/*-- Overlay Color --*/
[data-overlay="light"]::before {
  background-color: var(--color-white); }

[data-overlay="dark"]::before {
  background-color: var(--color-black); }

/*-- Overlay Opacity --*/
[data-opacity="1"]::before {
  opacity: 0.1; }

[data-opacity="2"]::before {
  opacity: 0.2; }

[data-opacity="3"]::before {
  opacity: 0.3; }

[data-opacity="4"]::before {
  opacity: 0.4; }

[data-opacity="5"]::before {
  opacity: 0.5; }

[data-opacity="6"]::before {
  opacity: 0.6; }

[data-opacity="7"]::before {
  opacity: 0.7; }

[data-opacity="8"]::before {
  opacity: 0.8; }

[data-opacity="9"]::before {
  opacity: 0.9; }

.header-area {
  position: relative;
  z-index: 5;
  background: #080b18;
  box-shadow: 0 8px 77px 0 rgba(0, 0, 0, 0.71);
  border-bottom: 1px solid #262a3a;
  padding: 0; }
  @media (max-width: 991px) {
    .header-area .xb-header {
      padding: 10px 0; }
    .side-menu a {
      margin-left: 20px;
    }
  }

.side-menu a {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  width: 50px;
  height: 50px; }
  .side-menu a span {
    position: absolute;
    left: 47px;
    top: 50%;
    -webkit-transform: translateY(-50%);
    -ms-transform: translateY(-50%);
    transform: translateY(-50%);
    text-transform: uppercase;
    color: var(--color-white);
    font-size: 14px;
    font-weight: 600;
    padding-left: 8px; }
  .side-menu a svg {
    position: absolute;
    top: 50%;
    left: 50%;
    -webkit-transform: translate(-50%, -50%);
    -ms-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
    z-index: -1; }
    .side-menu a svg path {
      fill: var(--color-primary); }

.heade-style-two {
  background: transparent;
  border-bottom: none;
  box-shadow: none;
  position: absolute;
  left: 0;
  right: 0; }
  .heade-style-two .main-menu ul li a {
    padding: 33px 0; }
    .heade-style-two .main-menu ul li a::before {
      display: none; }
  .heade-style-two .stricked-menu {
    background-color: #0D0B33; }

.header-transparent {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  z-index: 3; }

.header-bg::before {
  position: absolute;
  left: 0;
  top: 0;
  width: 43.8%;
  height: 100%;
  content: "";
  background-color: var(--color-primary);
  z-index: -1; }
  @media only screen and (min-width: 1200px) and (max-width: 1500px) {
    .header-bg::before {
      width: 34%; } }
  @media (max-width: 1199px) {
    .header-bg::before {
      width: 50%; } }
  @media (max-width: 767px) {
    .header-bg::before {
      width: 100%; } }

.xb-header_btn a {
  font-family: var(--font-heading);
  line-height: 28px;
  letter-spacing: -0.16px;
  color: var(--color-primary);
  padding: 10px 20px;
  border: 1px solid var(--color-primary); }

.side-menu-4 {
  margin-left: 40px; }

.header-btn .login-btn {
  font-size: 16px;
  font-weight: 600;
  color: #ebf7fd;
  border: 1px solid #41445c;
  padding: 10px 21px;
  border-radius: 50px;
  display: flex;
  align-items: center;
  justify-content: center; }
.header-btn .login-btn svg {
  height: 16px;
  margin-right: 6px;
}
.header-btn .login-btn svg path {
  fill: var(--color-primary);
  -webkit-transition: .3s;
  -moz-transition: .3s;
  -ms-transition: .3s;
  -o-transition: .3s;
  transition: .3s;
}
.header-btn .login-btn:hover svg path {
  fill: #080B18;
}
  @media (max-width: 767px) {
    .header-btn .login-btn {
      display: none; } }
  .header-btn .login-btn i {
    color: var(--color-primary);
    padding-right: 6px; }

.xb-nav-mobile {
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #41445c;
  color: var(--color-white);
  font-size: 18px;
  border-radius: 50%;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  -ms-border-radius: 50%;
  -o-border-radius: 50%; }
  .xb-nav-mobile:hover {
    background-color: var(--color-primary);
    border-color: var(--color-primary);
    color: var(--color-black); }

.stricked-menu {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 999;
  top: 0;
  -webkit-transform: translateY(-100%);
  -khtml-transform: translateY(-100%);
  -moz-transform: translateY(-100%);
  -ms-transform: translateY(-100%);
  -o-transform: translateY(-100%);
  transform: translateY(-100%);
  -webkit-transition: 0.6s cubic-bezier(0.24, 0.74, 0.58, 1);
  -khtml-transition: 0.6s cubic-bezier(0.24, 0.74, 0.58, 1);
  -moz-transition: 0.6s cubic-bezier(0.24, 0.74, 0.58, 1);
  -ms-transition: 0.6s cubic-bezier(0.24, 0.74, 0.58, 1);
  -o-transition: 0.6s cubic-bezier(0.24, 0.74, 0.58, 1);
  transition: 0.6s cubic-bezier(0.24, 0.74, 0.58, 1);
  visibility: hidden;
  background-color: #080B18; }
  .stricked-menu .main-menu ul li a {
    padding: 30px 0; }

.stricked-menu.stricky-fixed {
  -webkit-transform: translateY(0%);
  -khtml-transform: translateY(0%);
  -moz-transform: translateY(0%);
  -ms-transform: translateY(0%);
  -o-transform: translateY(0%);
  transform: translateY(0%);
  visibility: visible;
  -webkit-box-shadow: 0 3px 18px rgba(2, 21, 78, 0.09);
  -khtml-box-shadow: 0 3px 18px rgba(2, 21, 78, 0.09);
  -moz-box-shadow: 0 3px 18px rgba(2, 21, 78, 0.09);
  -ms-box-shadow: 0 3px 18px rgba(2, 21, 78, 0.09);
  -o-box-shadow: 0 3px 18px rgba(2, 21, 78, 0.09);
  box-shadow: 0 3px 18px rgba(2, 21, 78, 0.09); }

/* Navigation css */
.main-menu {
  display: flex;
  align-items: center;
  flex-grow: 1; }
  .main-menu__wrap {
    position: static; }
  .main-menu ul {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    list-style: none;
    padding: 0;
    margin: 0; }
    .main-menu ul li {
      position: relative; }
      .main-menu ul li:not(:last-child) {
        margin-right: 55px; }
      .main-menu ul li .sub-menu li {
        margin-right: 0; }
      .main-menu ul li a {
        display: inline-block;
        text-decoration: none;
        font-size: 18px;
        font-weight: 400;
        color: var(--color-white);
        padding: 38px 0;
        position: relative;
        line-height: 22px;
        text-transform: capitalize;
        position: relative;
        -webkit-transition: 0.3s;
        -o-transition: 0.3s;
        transition: 0.3s; }
.main-menu ul li a:hover {
  color: #fff;
}
        .main-menu ul li a::before {
          position: absolute;
          content: '';
          bottom: 0;
          height: 2px;
          width: 0%;
          place-items: center;
          left: auto;
          right: 0;
          z-index: -1;
          -webkit-transition: all 0.25s cubic-bezier(0.645, 0.045, 0.355, 1);
          -khtml-transition: all 0.25s cubic-bezier(0.645, 0.045, 0.355, 1);
          -moz-transition: all 0.25s cubic-bezier(0.645, 0.045, 0.355, 1);
          -ms-transition: all 0.25s cubic-bezier(0.645, 0.045, 0.355, 1);
          -o-transition: all 0.25s cubic-bezier(0.645, 0.045, 0.355, 1);
          transition: all 0.25s cubic-bezier(0.645, 0.045, 0.355, 1);
          background: var(--color-primary); }
      .main-menu ul li:hover > a:before, .main-menu ul li.active > a::before {
        width: 100%;
        left: 0;
        right: auto; }
      .main-menu ul li.menu-item-has-children > a::after {
        content: "\f078";
        transform: translateY(-1px);
        display: inline-block;
        padding-left: 4px;
        font-family: "Font Awesome 5 Pro";
        font-weight: 900;
        font-size: 12px; }
    .main-menu ul li.menu-item-has-children:hover > .sub-menu {
      opacity: 1;
      visibility: visible;
      pointer-events: all;
      transform: none !important;
    }
    .main-menu ul li .sub-menu {
      -webkit-box-orient: vertical;
      -webkit-box-direction: normal;
      -ms-flex-direction: column;
      flex-direction: column;
      position: absolute;
      min-width: 245px;
      top: 100%;
      opacity: 0;
      visibility: hidden;
      background: #080b18;
      left: 0;
      padding: 20px 0;
      -webkit-transition: 0.3s ease-out;
      -o-transition: 0.3s ease-out;
      transition: 0.3s ease-out;
      z-index: 3;
      text-align: left;
      pointer-events: none;
      box-shadow: 0 0.5rem 1.875rem rgba(0, 0, 0, 0.1);
      transform: translate3d(0, 18px, 0);
    }
        .main-menu ul li .sub-menu li a {
          color: var(--color-white);
          padding: 8px 30px;
          display: block;
          margin: 0;
          font-size: 16px; }
          .main-menu ul li .sub-menu li a::before {
            display: none; }
        .main-menu ul li .sub-menu li:hover > a {
          letter-spacing: 1px; }
        .main-menu ul li .sub-menu ul {
          left: 100%;
          top: 0px; }
        .main-menu ul li .sub-menu .active > a {
          letter-spacing: 1px; }
      .main-menu ul li.has-mega-menu {
        position: static; }
        .main-menu ul li.has-mega-menu li {
          width: 100%; }
      .main-menu ul li .mega-menu {
        width: 100%;
        left: 0;
        right: 0;
        position: absolute;
        box-shadow: none;
        background-color: transparent;
        padding: 0;
        max-width: 1287px;
        margin: 0 auto;
        padding: 40px 35px;
        padding-bottom: 0;
        background-color: #151534; }
        .main-menu ul li .mega-menu .mega-menu-content a {
          padding: 0; }
        .main-menu ul li .mega-menu .mega-menu-content .col {
          margin-bottom: 30px; }
        .main-menu ul li .mega-menu .mega-menu-content .demo-pic {
          margin-bottom: 10px;
          position: relative;
          height: 350px;
          overflow: hidden;
          -webkit-transition: 0.3s;
          -o-transition: 0.3s;
          transition: 0.3s; }
          .main-menu ul li .mega-menu .mega-menu-content .demo-pic:hover {
            -webkit-transform: translateY(-10px);
            -ms-transform: translateY(-10px);
            transform: translateY(-10px); }
          @media (max-width: 1199px) {
            .main-menu ul li .mega-menu .mega-menu-content .demo-pic {
              height: 230px; } }
          .main-menu ul li .mega-menu .mega-menu-content .demo-pic.coming-soon:hover {
            -webkit-transform: translateY(0px);
            -ms-transform: translateY(0px);
            transform: translateY(0px); }
        .main-menu ul li .mega-menu h3 {
          font-size: 15px;
          text-align: center;
          font-family: var(--font-body);
          font-weight: 500; }

.main-menu ul li.menu-last ul.sub-menu {
  right: 0;
  left: auto; }

.main-menu ul li.menu-last ul.sub-menu ul {
  right: auto;
  left: -100%; }

.main-menu ul li ul.sub-menu .menu-item-has-children > a::after {
  position: absolute;
  top: 9px;
  right: 15px;
  content: "\f105";
  font-size: 13px; }

.xb-contact {
  position: relative;
  top: -110px;
  border-radius: 22px;
  overflow: hidden; }

.xb-inner {
  border-radius: 20px 0 0 20px;
  background: linear-gradient(335deg, #435fb2 0%, #111b5e 66.48%, #0c1449 100%);
  padding: 60px 70px 70px 70px;
  background-size: cover; }
  @media (max-width: 767px) {
    .xb-inner {
      padding: 43px 25px 50px 25px; } }
  .xb-inner .xb-item--title {
    font-size: 40px;
    line-height: 50px;
    letter-spacing: -0.04px;
    margin-bottom: 45px; }
    @media (max-width: 767px) {
      .xb-inner .xb-item--title {
        font-size: 30px;
        line-height: 39px;
        letter-spacing: 0;
        margin-bottom: 33px; } }
  .xb-inner .xb-item--field {
    position: relative;
    margin-bottom: 23px; }

    .xb-inner .xb-item--field input {
      background: none;
      border-bottom: 2px solid #2c398c;
      padding: 0;
      font-weight: 500;
      line-height: 24px;
      font-size: 16px;
      padding: 0 0 0 29px;
      height: 56px;
      color: var(--colo-white); }
      .xb-inner .xb-item--field input::placeholder {
        color: var(--color-default);
        opacity: 0.5; }
  .xb-inner .xb-item--checkbox {
    padding-left: 37px;
    padding-top: 30px; }
    .xb-inner .xb-item--checkbox .form-check-label {
      color: #b0b3c7; }
      .xb-inner .xb-item--checkbox .form-check-label a {
        color: #b0b3c7;
        text-decoration: underline; }
  .xb-inner .xb-item--content {
    padding-left: 10px; }
    .xb-inner .xb-item--content a {
      color: var(--color-default);
      text-decoration: underline; }
  .xb-inner .xb-item--contact-btn {
    padding-top: 44px; }
    .xb-inner .xb-item--contact-btn .them-btn {
      width: 100%;
      height: 50px;
      padding: 0;
    }

.form-check input {
  border: 2px solid #B0B3C7 !important;
  background-color: transparent !important;
  margin-top: 6px; }

.testimonial-wrap {
  border-radius: 0 20px 20px 0;
  background: linear-gradient(335deg, #435fb2 0%, #111b5e 66.48%, #0c1449 100%);
  padding: 70px 90px 49px 68px;
  margin-left: -1px;
  background-size: cover; }
  @media (max-width: 767px) {
    .testimonial-wrap {
      padding: 30px; } }

.testimonial-slider {
  padding-bottom: 180px; }
  @media (max-width: 1199px) {
    .testimonial-slider {
      padding-bottom: 70px; } }
  .testimonial-slider .swiper-pagination .swiper-pagination-bullet {
    height: 10px;
    width: 10px;
    border-radius: 2px;
    background: #2dd98f;
    opacity: 0.1; }
  .testimonial-slider .swiper-pagination .swiper-pagination-bullet-active {
    opacity: 1; }

.xb-testimonial .xb-item--avater {
  margin-bottom: 80px; }
  @media (max-width: 1199px) {
    .xb-testimonial .xb-item--avater {
      margin-bottom: 35px; } }
.xb-testimonial .xb-item--img {
  margin-right: 20px; }
  .xb-testimonial .xb-item--img img {
    border-radius: 10px; }
.xb-testimonial .xb-item--holder {
  transform: translateY(6px); }
.xb-testimonial .xb-item--nationality span {
  font-size: 18px;
  font-weight: 500;
  line-height: 24px;
  padding-left: 10px; }
.xb-testimonial .xb-item--title {
  font-size: 22px;
  line-height: 34px;
  letter-spacing: -0.03px;
  padding-top: 8px; }
.xb-testimonial .xb-item--sub-title {
  font-family: var(--font-heading);
  line-height: 34px;
  letter-spacing: -0.03px;
  font-weight: 500; }
.xb-testimonial .xb-item--comment {
  font-size: 22px;
  line-height: 33px;
  color: var(--color-white); }

.xb-footer-bottom {
  background: #080b18;
  border-radius: 20px;
  padding: 60px 70px 32px;
  position: relative;
  margin: 0 15px;
}
  @media (max-width: 767px) {
    .xb-footer-bottom {
      padding: 49px 50px 30px; } }
  .xb-footer-bottom .footer-copyright {
    padding-top: 22px; }
.xb-footer-link a:hover {
  color: #fff;
}
.xb-footer-wrap {
  padding-bottom: 30px;
  align-items: flex-start;
  position: relative; }
  .xb-footer-wrap::before {
    content: '';
    position: absolute;
    height: 1px;
    width: 100%;
    left: 0;
    bottom: 0;
    background: linear-gradient(136deg, #080b18 0%, #2dd98f 47.79%, #080b18 100%); }
  .xb-footer-wrap .xb-item--footer_widget span {
    font-weight: 500;
    line-height: 30px; }
  .xb-footer-wrap .xb-item--footer_widget-list {
    list-style: none;
    margin-top: 20px; }
    .xb-footer-wrap .xb-item--footer_widget-list li a {
      font-size: 16px;
      font-weight: 600;
      line-height: 35px;
      color: var(--color-default); }
      .xb-footer-wrap .xb-item--footer_widget-list li a:hover {
        color: #fff; }
    .xb-footer-wrap .xb-item--footer_widget-list .underline {
      text-decoration: underline; }
  .xb-footer-social {
    margin-top: 19px; }
    .xb-footer-social li:not(:last-child) {
      margin-right: 6px; }
    .xb-footer-social a {
      height: 40px;
      width: 40px;
      border-radius: 5px;
      background: #21232f;
      display: flex;
      align-items: center;
      justify-content: center;
      color: var(--color-white);
      -webkit-transition: 0.4s;
      -o-transition: 0.4s;
      transition: 0.4s; }
      .xb-footer-social a:hover {
        background: var(--color-primary);
        color: var(--color-black); }
.xb-footer-social a svg {
  height: 16px;
}
.xb-footer-social a svg path {
  -webkit-transition: .3s;
  -moz-transition: .3s;
  -ms-transition: .3s;
  -o-transition: .3s;
  transition: .3s;
  fill: #fff;
}
.xb-footer-social a:hover svg path {
  fill: var(--color-black);
}

@media (max-width: 991px) {
  .footer-shape {
    display: none; } }
.footer-shape .shape {
  position: absolute; }
  .footer-shape .shape--1 {
    left: 60px;
    bottom: 93px; }
  .footer-shape .shape--2 {
    position: absolute;
    right: 20px;
    bottom: 18%; }
    @media only screen and (min-width: 1200px) and (max-width: 1500px) {
      .footer-shape .shape--2 {
        bottom: 10%;
        max-width: 100px; } }
    @media (max-width: 1199px) {
      .footer-shape .shape--2 {
        right: 15px;
        bottom: 11%;
        max-width: 80px; } }

.xb-contact-form {
  backdrop-filter: blur(4.8000001907px);
  background: rgba(74, 33, 239, 0.18);
  border: 1px solid rgba(255, 255, 255, 0.28);
  border-radius: 20px;
  padding: 20px; }
  .xb-contact-form .xb-inner {
    border-radius: 20px;
    background: #0d0b33;
    padding: 52px 60px 60px 60px;
    position: relative;
    border: none;
    height: 100%;
    margin-right: 18px;
    z-index: 1; }
    @media (max-width: 767px) {
      .xb-contact-form .xb-inner {
        padding: 40px 25px 40px 25px;
        margin-right: 0; } }
    .xb-contact-form .xb-inner::before {
      content: '';
      top: -1px;
      left: -1px;
      right: -1px;
      bottom: -1px;
      padding: 1px;
      height: 100%;
      position: absolute;
      border-radius: 20px;
      -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
      mask-composite: add, add;
      mask-composite: add, add;
      -webkit-mask-composite: source-out;
      mask-composite: exclude;
      background: linear-gradient(162deg, #8f7bff 0%, #413886 28.35%, #0f0d39 73.81%, #2dd98f 100%);
      z-index: -1; }
  .xb-contact-form .xb-item--sub-title {
    font-size: 14px;
    font-weight: 600;
    line-height: 30px; }
  .xb-contact-form .xb-item--title {
    font-size: 26px;
    line-height: 36px;
    margin-bottom: 10px;
    margin-top: 18px;
    margin-bottom: 20px;
    max-width: 401px; }
  .xb-contact-form .xb-item--field {
    margin-bottom: 18px; }
    .xb-contact-form .xb-item--field input,
    .xb-contact-form .xb-item--field textarea {
      height: 60px;
      border: 1px solid rgba(255, 255, 255, 0.08);
      border-radius: 10px;
      background: #151246;
      padding: 0 48px;
      color: var(--color-white); }
.xb-contact-form .xb-item--field textarea {
  height: 152px;
}
      .xb-contact-form .xb-item--field input::placeholder,
      .xb-contact-form .xb-item--field textarea::placeholder {
        color: var(--color-white);
        opacity: .5; }
  .xb-contact-form .xb-item--text-msg {
    position: relative; }
    .xb-contact-form .xb-item--text-msg span {
      position: absolute;
      top: 10px;
      left: 30px; }
  .xb-contact-form .xb-item--massage {
    border: 1px solid rgba(255, 255, 255, 0.08);
    border-radius: 10px;
    background: #151246;
    padding: 0 48px;
    height: 152px;
    padding-top: 10px;
    color: var(--color-white); }
    .xb-contact-form .xb-item--massage::-webkit-input-placeholder {
      color: #888686;
      opacity: 1; }
    .xb-contact-form .xb-item--massage::-moz-placeholder {
      color: #888686;
      opacity: 1; }
    .xb-contact-form .xb-item--massage:-ms-input-placeholder {
      color: #888686;
      opacity: 1; }
    .xb-contact-form .xb-item--massage:-moz-placeholder {
      color: #888686;
      opacity: 1; }
    .xb-contact-form .xb-item--massage::placeholder {
      color: var(--color-white);
      opacity: .5; }
  .xb-contact-form .xb-item--contact-btn .them-btn {
    width: 315px;
    height: 70px;
    padding: 0; }
    @media (max-width: 767px) {
      .xb-contact-form .xb-item--contact-btn .them-btn {
        width: 100%; } }

.footer-apps {
  background: #0d0b33;
  border-radius: 20px;
  position: relative;
  padding: 13px 29px 0 28px;
  z-index: 1; }
  .footer-apps::before {
    content: '';
    top: -1px;
    left: -1px;
    right: -1px;
    bottom: -1px;
    padding: 1px;
    height: 100%;
    position: absolute;
    border-radius: 20px;
    -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    mask-composite: add, add;
    mask-composite: add, add;
    -webkit-mask-composite: source-out;
    mask-composite: exclude;
    background: linear-gradient(334deg, #6780d2 0%, #2f3b8d 100%);
    z-index: -1; }
  .footer-apps .xb-item--img {
    height: 360px;
    overflow: hidden; }
  .footer-apps .xb-item--shadow {
    position: absolute;
    left: 0;
    right: 0;
    bottom: -82px; }
  .footer-apps .xb-item--holder {
    padding: 11px 30px 62px  63px; }
    @media (max-width: 1199px) {
      .footer-apps .xb-item--holder {
        padding: 30px 0; } }
  .footer-apps .xb-item--title {
    text-transform: capitalize; }
  .footer-apps .xb-item--button {
    display: flex;
    padding-top: 33px; }
    .footer-apps .xb-item--button .apps-btn {
      height: 50px;
      width: 162px;
      margin-right: 20px;
      padding: 0; }
      @media (max-width: 767px) {
        .footer-apps .xb-item--button .apps-btn {
          font-size: 15px; } }
      .footer-apps .xb-item--button .apps-btn span {
        margin-right: 10px;
        display: flex;
        align-items: center; }

.xb-contact_info .contact-method {
  display: flex;
  align-items: center;
  font-size: 24px;
  line-height: 34px;
  font-weight: 500;
  color: var(--color-white); }
  .xb-contact_info .contact-method span {
    height: 56px;
    width: 56px;
    border-radius: 100%;
    background: #151246;
    margin-right: 20px;
    display: flex;
    align-items: center;
    justify-content: center; }


  .xb-footer-widget .xb-item--footer_nav {
    margin-top: -10px;
    padding-top: 30px;
  }
    @media (max-width: 767px) {
      .xb-footer-widget .xb-item--footer_nav {
        justify-content: center; } }
    .xb-footer-widget .xb-item--footer_nav li {
      margin-right: 40px;
      margin-top: 10px; }
      .xb-footer-widget .xb-item--footer_nav li a {
        font-size: 18px;
        font-weight: 500;
        color: var(--color-white); }
        .xb-footer-widget .xb-item--footer_nav li a:hover {
          text-decoration: underline; }
  .footer-newsletter {
    position: relative;
    margin-top: 30px;
  }
    @media (max-width: 991px) {
      .footer-newsletter {
        margin-top: 30px; } }
    @media (max-width: 767px) {
      .footer-newsletter {
        width: 100%; } }
    .footer-newsletter::before {
      content: '';
      position: absolute;
      height: 120px;
      width: 1px;
      background: #2a284b;
      left: 0;
      top: -30px;
      margin-left: -159px; }
.footer-newsletter .icon::before {
  position: absolute;
  top: 22px;
  left: 30px;
  width: 18px;
  height: 18px;
  content: "";
  background-repeat: no-repeat;
  z-index: 1;
  background-image: url("../img/sms-tracking.png");
}
    .footer-newsletter input {
      border: 1px solid rgba(255, 255, 255, 0.08);
      border-radius: 30px;
      width: 485px;
      height: 60px;
      background: #151246;
      padding-left: 54px;
      color: var(--color-white); }
      @media (max-width: 767px) {
        .footer-newsletter input {
          width: 100%; } }
    .footer-newsletter button {
      position: absolute;
      top: 50%;
      right: 0;
      transform: translateY(-50%);
      line-height: 50px;
      height: 40px;
      width: 133px;
      border-radius: 30px;
      background: #0d0b33;
      color: var(--color-white);
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 10px; }

@media (max-width: 767px) {
  .footer-copyright {
    justify-content: center;
    text-align: center; } }
@media (max-width: 767px) {
  .footer-copyright .footer-link {
    margin-top: 20px; } }
.footer-copyright .footer-link li {
  margin-right: 11px; }
  .footer-copyright .footer-link li a {
    color: var(--color-white);
    height: 40px;
    width: 40px;
    border-radius: 50px;
    background: #151246;
    display: flex;
    align-items: center;
    justify-content: center;
    -webkit-transition: 0.4s;
    -o-transition: 0.4s;
    transition: 0.4s; }
    .footer-copyright .footer-link li a:hover {
      background: var(--color-primary);
      color: #080b18; }

.second-footer {
  padding-top: 409px;
  z-index: 1; }
  @media (max-width: 991px) {
    .second-footer {
      padding-top: 250px; } }
  @media (max-width: 767px) {
    .second-footer {
      padding-top: 180px; } }

.footer-bg {
  position: absolute;
  height: 100%;
  width: 100%;
  top: 0;
  left: 0;
  z-index: -1; }


  @media (max-width: 991px) {
    .xb-round_ranner {
      top: 9%; } }
  @media (max-width: 767px) {
    .xb-round_ranner {
      top: 6%; } }
  .xb-round_ranner .spin-img {
    animation: spin 20s infinite linear;
    -webkit-animation: spin 20s infinite linear; }
  .xb-round_ranner span {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    height: 112px;
    width: 112px;
    background: linear-gradient(152deg, #8b6fff 0%, #0d0b33 51%, #2dd98f 100%);
    border-radius: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid #623bfd; }
.token-info-progress {
  width: 100%;
  height: 8px;
  background: rgba(255, 255, 255, 0.3);
  overflow: unset;
}
.token-info-progress .progress-bar {
  background-color: var(--color-primary);
  overflow: unset;
  position: relative;
}
.token-info-progress .progress-bar:before {
  height: 30px;
  width: 8px;
  content: "";
  background-color: var(--color-primary);
  position: absolute;
  top: -11px;
  right: 0;
}
.ft-shape .shape {
  position: absolute;
  z-index: -1; }
  .ft-shape .shape--1 {
    left: 7%;
    top: 44%; }
  .ft-shape .shape--2 {
    left: 5.3%;
    top: 40%; }
  .ft-shape .shape--3 {
    right: 5.3%;
    top: 38%; }
  .ft-shape .shape--4 {
    right: 7%;
    top: 43%; }

.footer-line .line {
  position: absolute; }
  .footer-line .line--1 {
    left: 4%;
    bottom: 21%; }
  .footer-line .line--2 {
    right: 4%;
    bottom: 25%; }

.partner-slider .xb-item--brand {
  border: 1px solid #21294d;
  border-radius: 32px;
  max-width: 289px;
  height: 202px;
  background: #080b18;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  overflow: hidden;
  position: relative;
  margin: 0 15px;
  z-index: 1; }
  .partner-slider .xb-item--brand span {
    font-weight: 500;
    color: var(--color-white);
    letter-spacing: 0.01px;
    text-transform: capitalize;
    display: block;
    padding-top: 22px; }
  .partner-slider .xb-item--brand::after, .partner-slider .xb-item--brand::before {
    content: '';
    height: 192px;
    width: 192px;
    filter: blur(32px);
    background: radial-gradient(34.71% 34.71% at 56.45% 63.48%, #8a6cff 23.4400004148%, #8a6cff 72.9200005531%);
    opacity: 0.2;
    transition: .3s;
    z-index: -1; }
  .partner-slider .xb-item--brand::after {
    position: absolute;
    top: -59.5%;
    left: -36%; }
  .partner-slider .xb-item--brand::before {
    position: absolute;
    bottom: -59.5%;
    right: -36%; }
  .partner-slider .xb-item--brand:hover::after, .partner-slider .xb-item--brand:hover::before {
    opacity: 1; }

.patners-title {
  position: relative;}
  .patners-title span {
    font-weight: 500;
    line-height: 30px;
    text-transform: uppercase;
    display: block;
    color: var(--color-white); }
    .patners-title span img {
      padding: 9px;
      min-width: 32px;
      transform: translateY(-2px);
    }

.partner-slider-two {
  margin-left: -57px;
  margin-right: -57px; }
  .partner-slider-two .xb-item--brand {
    position: relative;
    height: 72px;
    background: #0d0b33;
    border-radius: 10px;
    margin: 0;
    z-index: 1;
    border: none; }
    .partner-slider-two .xb-item--brand::after {
      display: none; }
    .partner-slider-two .xb-item--brand::before {
      content: "";
      position: absolute;
      opacity: 1;
      width: 100%;
      height: 100%;
      z-index: -1;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      border-radius: 10px;
      padding: 1px;
      -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
      mask-composite: add, add;
      mask-composite: add, add;
      -webkit-mask-composite: source-out;
      mask-composite: exclude;
      background: linear-gradient(177deg, #8b6fff 0%, #0d0b33 51%, #2dd98f 100%); }

@media (max-width: 1199px) {
  .partners-two {
    padding-top: 120px; } }
@media (max-width: 991px) {
  .partners-two {
    padding-top: 50px; } }

.about {
  z-index: 1;
  position: relative; }
  .about .about-wrap .xb-item--title {
    font-size: 55px;
    font-weight: 700;
    line-height: 60px;
    letter-spacing: -0.03px;
    text-transform: capitalize;
    padding-bottom: 32px; }
    @media (max-width: 767px) {
      .about .about-wrap .xb-item--title {
        font-size: 30px;
        line-height: 45px;
        padding-bottom: 15px; } }
  .about .about-wrap .xb-item--title1 {
    font-size: 30px;
    padding-bottom: 12px; }
  .about .about-wrap .xb-item--content {
    font-size: 20px;
    font-weight: 500;
    line-height: 32px;
    padding-bottom: 30px;
    max-width: 567px; }
  .about .about-wrap .xb-item--content1 {
    font-size: 18px;
    line-height: 30px; }
  .about .about-wrap .xb-item--list span {
    padding-right: 110px;
    margin-bottom: 33px;
    display: inline-block; }
    @media (max-width: 1199px) {
      .about .about-wrap .xb-item--list span {
        padding-right: 40px; } }
    .about .about-wrap .xb-item--list span svg {
      margin-right: 5px; }
  .about .about-img img {
    position: absolute;
    right: -14%;
    top: -113px;
    z-index: -1; }
    @media only screen and (min-width: 1200px) and (max-width: 1500px) {
      .about .about-img img {
        right: -26%; } }
    @media (max-width: 1199px) {
      .about .about-img img {
        right: -27%;
        top: -2%; } }
    @media (max-width: 991px) {
      .about .about-img img {
        position: relative;
        right: auto;
        left: 0;
        top: -12%;
        margin-bottom: -150px; } }

@media (max-width: 1199px) {
  .process {
    margin-bottom: 90px; } }

.xb-process.process-line::before {
  content: '';
  position: absolute;
  height: 1px;
  width: 970px;
  left: 133px;
  top: 182px;
  background: linear-gradient(90deg, #080b18 25.3%, #fff 29.39%, #080b18 33.43%, #080b18 70.52%, #fff 74.43%, #080b18 78.57%); }
  @media only screen and (min-width: 1200px) and (max-width: 1300px) {
    .xb-process.process-line::before {
      width: 899px;
      left: 118px;
      top: 182px; } }
.xb-process::before {
  content: '';
  position: absolute;
  height: 1px;
  width: 158px;
  left: 84%;
  top: 42%;
  background-image: url(../img/prc_line.png);
  background-repeat: no-repeat;
  background-size: cover;
  z-index: -1; }
.xb-process .xb-item--icon {
  text-align: center;
  margin-bottom: 65px;
  position: relative;
}
.xb-process .xb-item--shape {
  position: absolute;
  right: 0;
  left: 0;
  top: 27%;
  z-index: -1; }
  @media (max-width: 991px) {
    .xb-process .xb-item--shape {
      width: 60%;
      margin: auto; } }
  @media (max-width: 767px) {
    .xb-process .xb-item--shape {
      width: 100%; } }
  .xb-process .xb-item--shape svg {
    overflow: hidden;
    width: 100%;
    height: 100%;
    display: block; }
.xb-process .xb-item--icon-inner {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1;
}
.xb-process .xb-item--holder {
  text-align: center;
  bottom: 68px;
  padding: 0 90px; }
  @media only screen and (min-width: 1200px) and (max-width: 1500px) {
    .xb-process .xb-item--holder {
      padding: 0 75px; } }
  @media (max-width: 1199px) {
    .xb-process .xb-item--holder {
      bottom: 47px;
      padding: 0px 62px; } }
.xb-process .xb-item--title {
  font-size: 26px;
  letter-spacing: -0.02px;
  text-transform: capitalize;
  padding-bottom: 17px; }
  @media only screen and (min-width: 1200px) and (max-width: 1500px) {
    .xb-process .xb-item--title {
      font-size: 22px; } }
  @media (max-width: 1199px) {
    .xb-process .xb-item--title {
      font-size: 20px; } }
.xb-process .xb-item--content {
  line-height: 24px;
  display: inline-block;
  max-width: 230px; }
  @media (max-width: 1199px) {
    .xb-process .xb-item--content {
      font-size: 14px; } }
  @media (max-width: 1199px) {
    .xb-process .xb-item--content {
      font-size: 16px; } }

.process-col:last-child .xb-process::before {
  display: none; }

@media (max-width: 1199px) {
  .process-col:nth-child(2) .xb-process::before {
    display: none; } }
@media (max-width: 991px) {
  .process-col .xb-process::before {
    display: none; } }
.mt-none-130 {
  margin-top: -130px; }

.token-distribut {
  padding: 28px 54px 53px 50px;
  border: 1px solid #41445c;
  background: #080b18;
  border-radius: 20px;
  height: 100%;
}
  @media (max-width: 991px) {
    .token-distribut {
      margin-right: 0; } }
  @media (max-width: 767px) {
    .token-distribut {
      padding: 28px 25px 53px 25px; } }
  .token-distribut .xb-item--title {
    font-size: 30px;
    line-height: 87px;
    text-transform: capitalize; }
    @media (max-width: 767px) {
      .token-distribut .xb-item--title {
        font-size: 25px; } }
  .token-distribut .xb-item--list {
    margin-bottom: 55px; }
    .token-distribut .xb-item--list li {
      line-height: 24px;
      font-weight: 500;
      text-transform: capitalize;
      list-style: none;
      margin-bottom: 10px;
      position: relative;
      padding-left: 20px; }
      .token-distribut .xb-item--list li::after {
        content: '';
        height: 12px;
        width: 12px;
        background: #2DD98F;
        border-radius: 50px;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        position: absolute; }
      .token-distribut .xb-item--list li:nth-child(2)::after {
        background: #424CFF; }
      .token-distribut .xb-item--list li:nth-child(3)::after {
        background: #1BB8FF; }
      .token-distribut .xb-item--list li:nth-child(4)::after {
        background: #7BD6; }
      .token-distribut .xb-item--list li:nth-child(5)::after {
        background: #FF1CC2; }
      .token-distribut .xb-item--list li:nth-child(6)::after {
        background: #FFD529; }
      .token-distribut .xb-item--list li:nth-child(7)::after {
        background: #FFFFFF; }
  .token-distribut .xb-item--list-circle {
    position: relative;
    width: 395px;
    height: 241px; }
  .token-distribut .xb-item--circle {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 167px;
    width: 167px;
    border-radius: 50%;
    background: #2DD98F;
    position: absolute; }
    @media (max-width: 767px) {
      .token-distribut .xb-item--circle {
        height: 100px;
        width: 100px; } }
    .token-distribut .xb-item--circle span {
      font-family: var(--font-heading);
      font-weight: 700;
      line-height: 80px;
      letter-spacing: -0.03px;
      font-size: 50px;
      color: #080b18; }
      @media (max-width: 767px) {
        .token-distribut .xb-item--circle span {
          font-size: 30px; } }
    .token-distribut .xb-item--circle:nth-child(1) {
      top: 18px; }
    .token-distribut .xb-item--circle:nth-child(2) {
      background: #424CFF;
      height: 101.41px;
      width: 101.41px;
      bottom: 0;
      left: 167px; }
      .token-distribut .xb-item--circle:nth-child(2) span {
        font-size: 28px; }
      @media (max-width: 767px) {
        .token-distribut .xb-item--circle:nth-child(2) {
          left: 0; } }
    .token-distribut .xb-item--circle:nth-child(3) {
      background: #1BB8FF;
      height: 80.66px;
      width: 80.66px;
      left: 177px;
      top: 0; }
      .token-distribut .xb-item--circle:nth-child(3) span {
        font-size: 26px; }
      @media (max-width: 767px) {
        .token-distribut .xb-item--circle:nth-child(3) {
          left: 122px;
          top: -13px; } }
    .token-distribut .xb-item--circle:nth-child(4) {
      background: #7BD6FF;
      height: 76.06px;
      width: 76.06px;
      right: 75px;
      top: 68px; }
      .token-distribut .xb-item--circle:nth-child(4) span {
        font-size: 26px; }
      @media (max-width: 767px) {
        .token-distribut .xb-item--circle:nth-child(4) {
          right: 200px;
          top: 94px; } }
    .token-distribut .xb-item--circle:nth-child(5) {
      background: #FFD529;
      height: 56.47px;
      width: 56.47px;
      top: 0;
      right: 40px; }
      .token-distribut .xb-item--circle:nth-child(5) span {
        font-size: 20px; }
      @media (max-width: 767px) {
        .token-distribut .xb-item--circle:nth-child(5) {
          right: 106px; } }
    .token-distribut .xb-item--circle:nth-child(6) {
      background: #FF1CC2;
      height: 55.31px;
      width: 55.31px;
      bottom: 34px;
      right: 42px; }
      .token-distribut .xb-item--circle:nth-child(6) span {
        font-size: 20px; }
      @media (max-width: 767px) {
        .token-distribut .xb-item--circle:nth-child(6) {
          bottom: 0;
          right: 133px; } }
    .token-distribut .xb-item--circle:nth-child(7) {
      background: #FFFFFF;
      height: 49.55px;
      width: 49.55px;
      right: 0;
      top: 85px; }
      .token-distribut .xb-item--circle:nth-child(7) span {
        font-size: 20px; }
      @media (max-width: 767px) {
        .token-distribut .xb-item--circle:nth-child(7) {
          right: 100px; } }
.token-sale {
  background: #080b18;
  border: 1px solid #41445c;
  border-radius: 20px;
  padding: 40px 48px 33px 60px;
 }
  @media (max-width: 991px) {
    .token-sale {
      margin-left: 0; } }
  @media (max-width: 767px) {
    .token-sale {
      padding: 40px 20px 33px 20px; } }
  .token-sale:not(:last-child) {
    margin-bottom: 22px; }
  .token-sale .xb-item--title {
    font-size: 30px;
    line-height: 40px;
    text-transform: capitalize;
    margin-bottom: 67px;
    max-width: 430px; }
  .token-sale .xb-item--parcenteg {
    font-size: 20px;
    font-weight: 800;
    line-height: 40px;
    letter-spacing: -0.02px;
    padding-bottom: 6px;
    font-family: --fonts-heading; }
    .token-sale .xb-item--parcenteg span:nth-child(1) {
      color: #2DD98F;
      margin-right: 27%; }
      @media only screen and (min-width: 1200px) and (max-width: 1500px) {
        .token-sale .xb-item--parcenteg span:nth-child(1) {
          margin-right: 243px; } }
    .token-sale .xb-item--parcenteg span:nth-child(2) {
      color: #4D57FF;
      margin-right: 15%; }
    .token-sale .xb-item--parcenteg span:nth-child(3) {
      color: #1BB8FF;
      margin-right: 15%; }
    .token-sale .xb-item--parcenteg span:nth-child(4) {
      color: #7bd6ff;
      margin-right: 9%;
    }
  .token-sale .xb-item--token_progress {
    height: 10px;
    width: 100%;
    border-radius: 15px;
    margin-bottom: 23px;
    flex-wrap: nowrap; }
    .token-sale .xb-item--token_progress span:nth-child(1) {
      height: 10px;
      width: 47%;
      border-radius: 15px 0 0 15px;
      background: #2DD98F; }
    .token-sale .xb-item--token_progress span:nth-child(2) {
      height: 10px;
      width: 25%;
      background: #4D57FF; }
    .token-sale .xb-item--token_progress span:nth-child(3) {
      height: 10px;
      width: 17%;
      background: #1BB8FF; }
    .token-sale .xb-item--token_progress span:nth-child(4) {
      height: 10px;
      width: 12%;
      background: #7bd6ff;
      border-radius: 0 15px 15px 0; }
  .token-sale .xb-item--pro_list li {
    line-height: 24px;
    font-weight: 500;
    text-transform: capitalize;
    list-style: none;
    margin-bottom: 10px;
    position: relative;
    padding-left: 20px;
    margin-right: 20px; }
    .token-sale .xb-item--pro_list li::after {
      content: '';
      height: 12px;
      width: 12px;
      background: #2DD98F;
      border-radius: 50px;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      position: absolute; }
    .token-sale .xb-item--pro_list li:nth-child(2)::after {
      background: #424CFF; }
    .token-sale .xb-item--pro_list li:nth-child(3)::after {
      background: #1BB8FF; }
    .token-sale .xb-item--pro_list li:nth-child(4)::after {
      background: #7BD6; }
.token .model .xb-item--parcenteg span:nth-child(1) {
  color: #2DD98F;
  margin-right: 180px; }
  @media only screen and (min-width: 1200px) and (max-width: 1300px) {
    .token .model .xb-item--parcenteg span:nth-child(1) {
      margin-right: 158px; } }
  @media (max-width: 767px) {
    .token .model .xb-item--parcenteg span:nth-child(1) {
      margin-right: 20px; } }
.token .model .xb-item--parcenteg span:nth-child(2) {
  color: #4D57FF;
  margin-right: 100px; }
  @media (max-width: 767px) {
    .token .model .xb-item--parcenteg span:nth-child(2) {
      margin-right: 45px; } }
.token .model .xb-item--parcenteg span:nth-child(3) {
  color: #1BB8FF;
  margin-right: 100px; }
  @media only screen and (min-width: 1200px) and (max-width: 1300px) {
    .token .model .xb-item--parcenteg span:nth-child(3) {
      margin-right: 70px; } }
  @media (max-width: 767px) {
    .token .model .xb-item--parcenteg span:nth-child(3) {
      margin-right: 20px; } }
.token .model .xb-item--parcenteg span:nth-child(4) {
  color: #7bd6ff;
  margin-right: 60px; }
  @media only screen and (min-width: 1200px) and (max-width: 1300px) {
    .token .model .xb-item--parcenteg span:nth-child(4) {
      margin-right: 50px; } }
  @media (max-width: 767px) {
    .token .model .xb-item--parcenteg span:nth-child(4) {
      margin-right: 15px; } }
.token .model .xb-item--parcenteg span:nth-child(5) {
  color: #ffd136; }
.model .xb-item--token_progress span:nth-child(1) {
  width: 220px; }
.model .xb-item--token_progress span:nth-child(2) {
  width: 139px; }
.model .xb-item--token_progress span:nth-child(3) {
  width: 138px; }
.model .xb-item--token_progress span:nth-child(4) {
  width: 97px;
  border-radius: 0; }
.model .xb-item--token_progress span:nth-child(5) {
  height: 10px;
  width: 65px;
  background: #ffd136;
  border-radius: 0 15px 15px 0; }
.token .model .xb-item--pro_list li:nth-child(5)::after {
  background: #ffd136; }
.token .toke-shape .shape--one {
  position: absolute;
  left: 40px;
  bottom: 214px;
  z-index: -1; }
  @media only screen and (min-width: 1200px) and (max-width: 1500px) {
    .token .toke-shape .shape--one {
      bottom: 0;
      max-width: 80px; } }
.token .toke-shape .shape--two {
  position: absolute;
  bottom: 335px;
  right: 0;
  z-index: -1; }
  @media only screen and (min-width: 1200px) and (max-width: 1500px) {
    .token .toke-shape .shape--two {
      bottom: 0;
      right: 0;
      max-width: 120px; } }
  @media (max-width: 1199px) {
    .token .toke-shape .shape--two {
      bottom: 0;
      right: 0;
      max-width: 80px; } }

.feature-wrap {
  background: linear-gradient(335deg, #435fb2 0%, #111b5e 66.48%, #0c1449 100%);
  border-radius: 20px;
  padding: 25px 40px 50px;
  flex-wrap: nowrap;
  z-index: 1;
  position: relative;
}
  .feature-wrap .xb-item--title {
    font-size: 30px;
    line-height: 42px;
    text-transform: capitalize;
    max-width: 290px;
    letter-spacing: -0.02px;
    padding-bottom: 43px; }
  .feature-wrap .xb-item--content {
    line-height: 24px;
    max-width: 369px; }
  .feature-wrap .xb-item--holder {
    margin-top: 20px; }
  .feature-wrap .xb-item--feature-icon {
    margin-left: 25px;
    margin-top: 20px; }
.feature-wrap .xb-item--icon-inner {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
.feature-crypto {
  border-radius: 20px;
  border: 1px solid #374375; }
  .feature-crypto .mobile-crypto {
    padding: 50px 60px; }
    @media (max-width: 767px) {
      .feature-crypto .mobile-crypto {
        padding: 40px 20px; } }
    .feature-crypto .mobile-crypto .xb-item--sub-title {
      font-size: 14.4px;
      line-height: 21.6px;
      font-weight: 500;
      padding-bottom: 6px; }
      .feature-crypto .mobile-crypto .xb-item--sub-title span {
        display: flex;
        align-items: center; }
        .feature-crypto .mobile-crypto .xb-item--sub-title span svg {
          margin-right: 8px; }
      .feature-crypto .mobile-crypto .xb-item--sub-title .new-btn {
        height: 20px;
        width: 45px;
        border-radius: 20px;
        font-size: 10.4px;
        line-height: 15.6px;
        font-weight: 500;
        background: var(--color-primary);
        text-transform: uppercase;
        color: #080b18;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-left: 8px; }
    .feature-crypto .mobile-crypto .xb-item--title {
      font-size: 30px;
      line-height: 87px;
      text-transform: capitalize;
      margin-bottom: -10px; }
    .feature-crypto .mobile-crypto .xb-item--content {
      max-width: 450px; }
    .feature-crypto .mobile-crypto .xb-item--crypto-list {
      list-style: none;
      padding-top: 15px; }
      .feature-crypto .mobile-crypto .xb-item--crypto-list li {
        margin-bottom: 10px; }
        .feature-crypto .mobile-crypto .xb-item--crypto-list li svg {
          margin-right: 5px; }
    .feature-crypto .mobile-crypto .xb-item--crypto-btn {
      display: flex;
      flex-wrap: wrap;
      padding-top: 40px;
      margin: -9px; }
      .feature-crypto .mobile-crypto .xb-item--crypto-btn .crp-btn {
        height: 50px;
        width: 162px;
        padding: 0;
        margin: 9px; }
        .feature-crypto .mobile-crypto .xb-item--crypto-btn .crp-btn span {
          margin-right: 10px;
          display: flex;
          align-items: center; }
.mobile-crypto .xb-item--crypto-btn .crp-btn span svg,
.footer-apps .xb-item--button .apps-btn span svg {
  height: 17px;
}
.mobile-crypto .xb-item--crypto-btn .crp-btn .btn_label {
  margin-right: 0;
}
  .feature-crypto .cry-mobile-img {
    margin-left: -20px; }
.feature .feature-shape {
  position: absolute;
  bottom: -209px;
  text-align: center;
  left: 112px;
  right: 0; }

.xb-feature {
  border: 1px dashed rgba(255, 255, 255, 0.28);
  border-radius: 20px;
  padding: 15px;
  backdrop-filter: blur(4.8000001907px);
  background: rgba(74, 33, 239, 0.18);
  position: relative; }
  .xb-feature::before {
    content: '';
    left: -1px;
    right: -1px;
    bottom: -1px;
    padding: 1px;
    padding-top: 0;
    position: absolute;
    border-radius: 0 0 20px 20px;
    height: 50%;
    -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    mask-composite: add, add;
    mask-composite: add, add;
    -webkit-mask-composite: source-out;
    mask-composite: exclude;
    background: linear-gradient(180deg, #4f3bf0 0%, #2dd98f 100%); }
    @media only screen and (min-width: 1200px) and (max-width: 1300px) {
      .xb-feature::before {
        display: none; } }
  .xb-feature::after {
    content: '';
    right: -29px;
    top: 50%;
    position: absolute;
    height: 2px;
    width: 28px;
    background: linear-gradient(90deg, #4c2cef 13.38%, #2dd98f 48.09%, #4c2bef 80%); }
    @media (max-width: 1300px) {
      .xb-feature::after {
        display: none; } }
  .xb-feature .xb-item--holder {
    background: #0d0b33;
    border-radius: 20px;
    padding: 23px 26px 35px; }
  .xb-feature .xb-item--img {
    padding-bottom: 25px;
    height: 100px; }
  .xb-feature .xb-item--title {
    font-size: 26px;
    line-height: 36px;
    padding-bottom: 13px; }
    @media only screen and (min-width: 1200px) and (max-width: 1500px) {
      .xb-feature .xb-item--title {
        font-size: 20px; } }
  .xb-feature .xb-item--content {
    line-height: 24px; }
    @media (max-width: 1199px) {
      .xb-feature .xb-item--content {
        max-width: 320px;
        margin: auto; } }

.feature-col:last-child .xb-feature::after {
  display: none; }

.top-border::before {
  content: '';
  top: -1px;
  padding: 1px;
  padding-bottom: 0;
  border-radius: 20px 20px 0 0;
  background: linear-gradient(180deg, #2dd98f 0%, #4f3bf0 100%); }

.feature-crypto-wrap {
  background: #0d0b33;
  margin: 0 20px;
  border-radius: 20px;
  z-index: 1; }
  @media (max-width: 991px) {
    .feature-crypto-wrap {
      padding-top: 100px;
      padding-bottom: 100px; } }
  .feature-crypto-wrap .section-title .title {
    max-width: 1040px;
    display: inline-block; }
  .feature-crypto-wrap::after, .feature-crypto-wrap::before {
    content: '';
    height: 673px;
    width: 512px;
    position: absolute;
    border-radius: 100%;
    background: #fff;
    filter: blur(400px);
    z-index: -1; }
  .feature-crypto-wrap::after {
    top: 48.7%;
    left: -135px; }
  .feature-crypto-wrap::before {
    top: 48.7%;
    right: -152px; }

.xb-freatue-crypto {
  padding: 10px 8px 10px 0;
  background: #110F41;
  flex-wrap: nowrap;
}
  @media (max-width: 991px) {
    .xb-freatue-crypto {
      background-position-x: 18%; } }
.xb-freatue-crypto .xb-item--holder {
  padding-left: 40px;
  margin-top: -17px;
  padding-top: 54px;
  padding-bottom: 54px;
}
  .xb-freatue-crypto .xb-item--title {
    font-size: 30px;
    line-height: 40px;
    max-width: 399px;
    padding-bottom: 35px; }
  .xb-freatue-crypto .xb-item--content {
    font-size: 18px;
    max-width: 364px; }
  .xb-freatue-crypto .xb-item--img {
    padding-left: 17px; }
    .xb-freatue-crypto .xb-item--img.col-img {
      padding-left: 34px; }

.xb-crypto-right-inner {
  border-radius: 20px;
  overflow: hidden;
  background: #110F41;
}
  .xb-crypto-right .xb-item--holder {
    padding: 52px 60px 65px 40px; }
    @media (max-width: 767px) {
      .xb-crypto-right .xb-item--holder {
        padding: 20px 20px 40px 25px; } }
  .xb-crypto-right .xb-item--content {
    max-width: 390px;
    font-size: 18px;
    padding-top: 37px; }
  .xb-crypto-right .xb-item--img {
    margin-top: -32px;
    width: 100%;
    text-align: center; }

.feature-left-inner {
  margin-right: -28px; }
  @media (max-width: 991px) {
    .feature-left-inner {
      margin-right: 0; } }

.feature-right-inner {
  padding-left: 30px; }
  @media (max-width: 991px) {
    .feature-right-inner {
      padding-left: 0; } }

.feature-bottom .xb-item--title {
  padding-top: 13px; }
.feature-bottom .xb-item--holder {
  padding: 96px 40px; }
  @media (max-width: 767px) {
    .feature-bottom .xb-item--holder {
      padding: 30px 20px; } }
.feature-bottom .xb-item--content {
  max-width: 444px; }

.feature-counter-wrap {
  border-top: 1px solid rgba(45, 217, 142, 0.2);
  border-bottom: 1px solid rgba(45, 217, 142, 0.2);
  padding: 15px 0; }
  .feature-counter-wrap .xb-feature-inner {
    position: relative; }
.feature-counter-wrap .xb-feature-inner::after {
  position: absolute;
  content: '';
  height: 114%;
  width: 38px;
  top: -16px;
  right: -30px;
  background-image: url(../img/fea-count-shape1.png);
  background-repeat: no-repeat;
}
.feature-counter-wrap .col-lg-3:last-child .xb-feature-inner::after {
  display: none;
}
      @media (max-width: 991px) {
        .feature-counter-wrap .xb-feature-inner::after {
          display: none; } }
  .feature-counter-wrap .xb-feature-item {
    text-align: center;
    border-radius: 20px;
    background: #151246;
    padding: 56px 0 66px 0;
    position: relative;
    z-index: 1; }
    .feature-counter-wrap .xb-feature-item::before {
      content: '';
      top: -1px;
      left: -1px;
      right: -1px;
      bottom: -1px;
      padding: 1px;
      position: absolute;
      border-radius: 20px;
      -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
      mask-composite: add, add;
      mask-composite: add, add;
      mask-composite: add, add;
      -webkit-mask-composite: source-out;
      mask-composite: exclude;
      background: linear-gradient(162deg, #8f7bff 0%, #413886 28.35%, #0f0d39 73.81%, #2dd98f 100%);
      z-index: -1; }
    .feature-counter-wrap .xb-feature-item .title {
      font-size: 60px;
      line-height: 72px;
      letter-spacing: -0.03px;
      text-transform: uppercase;
      padding-bottom: 10px; }
      .feature-counter-wrap .xb-feature-item .title .suffix {
        top: 6px;
        position: relative;
        padding-left: 3px; }
      @media (max-width: 1199px) {
        .feature-counter-wrap .xb-feature-item .title {
          font-size: 50px; } }
    .feature-counter-wrap .xb-feature-item .sub-title {
      font-size: 18px;
      font-weight: 500; }

@media only screen and (min-width: 1200px) and (max-width: 1500px) {
  .fea-shape {
    display: none; } }
@media (max-width: 1199px) {
  .fea-shape {
    display: none; } }
.fea-shape img:nth-child(1) {
  position: absolute;
  top: 42.5%;
  left: 74px; }
.fea-shape img:nth-child(2) {
  position: absolute;
  top: 22%;
  right: 74px; }

@media (max-width: 767px) {
  .xb-feature-shape {
    display: none; } }
.xb-feature-shape .shape {
  position: absolute;
  z-index: -1; }
  .xb-feature-shape .shape--1 {
    left: 30px;
    top: 60px; }
  .xb-feature-shape .shape--2 {
    right: 5%;
    top: 49%; }

.xb-team {
  margin-top: 22px;
  position: relative; }
  .xb-team .xb-item--img {
    overflow: hidden;
    border-radius: 25px;
    -webkit-border-radius: 25px;
    -moz-border-radius: 25px;
    -ms-border-radius: 25px;
    -o-border-radius: 25px; }
    .xb-team .xb-item--img img {
      -webkit-transition: 0.4s;
      -o-transition: 0.4s;
      transition: 0.4s;
      width: 100%; }
  .xb-team .xb-item--link {
    height: 40px;
    width: 40px;
    border-radius: 50px;
    background: #080b18;
    color: var(--color-white);
    border: 1px solid #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    -webkit-transition: 0.4s;
    -o-transition: 0.4s;
    transition: 0.4s;
    position: absolute;
    margin-top: -23px;
    left: 50%;
    -webkit-transform: translateX(-50%);
    -ms-transform: translateX(-50%);
    transform: translateX(-50%); }
.xb-team .xb-item--link svg {
  height: 16px;
}
.xb-team .xb-item--link svg path {
  fill: #fff;
  -webkit-transition: .3s;
  -moz-transition: .3s;
  -ms-transition: .3s;
  -o-transition: .3s;
  transition: .3s;
}
.xb-team:hover .xb-item--link svg path {
  fill: var(--color-black);
}
  .xb-team .xb-item--title {
    font-size: 22px;
    line-height: 40px;
    text-transform: capitalize;
    margin-bottom: -5px;
    padding-top: 31px; }
  .xb-team .xb-item--sub-title {
    line-height: 24px;
    text-transform: capitalize; }
  .xb-team:hover .xb-item--link {
    background: var(--color-primary);
    color: var(--color-black);
    border: 1px solid var(--color-primary); }
  .xb-team:hover .xb-item--img img {
    transform: scale(1.05); }
  .xb-team.xb-team1 {
    background: linear-gradient(239deg, #435fb2 0%, #111b5e 66.48%, #0c1449 100%);
    border-radius: 20px;}
    @media (max-width: 991px) {
      .xb-team.xb-team1 {
        margin-right: 0; } }
    .xb-team.xb-team1 .xb-item--link {
      margin-top: -20px; }
    .xb-team.xb-team1 .xb-item--title {
      font-size: 30px;
      margin-bottom: 5px;
      padding-top: 44px; }
    .xb-team.xb-team1 .xb-item--sub-title {
      padding-bottom: 40px;
      display: inline-block; }

.top-center {
  background-position: top center; }

@media (max-width: 991px) {
  .team-shape {
    display: none; } }
.team-shape .shape {
  position: absolute; }
  .team-shape .shape--1 {
    left: 70px;
    top: 49%; }
  .team-shape .shape--2 {
    left: 25px;
    bottom: 75px; }
    @media only screen and (min-width: 1200px) and (max-width: 1500px) {
      .team-shape .shape--2 {
        bottom: 0;
        max-width: 100px; } }
    @media (max-width: 1199px) {
      .team-shape .shape--2 {
        bottom: 0;
        max-width: 80px; } }
  .team-shape .shape--3 {
    right: 57px;
    bottom: 10px; }
  .team-shape .shape--4 {
    top: 32%;
    right: 0; }
    @media only screen and (min-width: 1200px) and (max-width: 1500px) {
      .team-shape .shape--4 {
        top: 7%;
        max-width: 70px; } }
    @media (max-width: 1199px) {
      .team-shape .shape--4 {
        top: 2%;
        max-width: 80px; } }

/*----------------------------------------*/
/* faq
/*----------------------------------------*/
.faq__blockchain {
  padding: 0 60px;
  background: #080b18;
  border-radius: 20px; }
  @media (max-width: 991px) {
    .faq__blockchain {
      padding: 0 30px; } }
  .faq__blockchain .accordion_box .block {
    border-color: #262932 !important; }
    .faq__blockchain .accordion_box .block .acc-btn {
      padding: 30px 0;
      font-family: 'Titillium Web', sans-serif;
      font-weight: 400; }
    .faq__blockchain .accordion_box .block .content {
      border-top: 0;
      padding-top: 0;
      padding-right: 70px; }
  .faq__blockchain .accordion.block.active-block .acc-btn .arrow {
    height: 40px;
    width: 40px;
    border-radius: 50px;
    background: var(--color-primary); }
    .faq__blockchain .accordion.block.active-block .acc-btn .arrow::after {
      opacity: 1; }
    .faq__blockchain .accordion.block.active-block .acc-btn .arrow span::before {
      opacity: 0; }
    .faq__blockchain .accordion.block.active-block .acc-btn .arrow span::after {
      opacity: 1; }
  .faq__blockchain .acc-btn::before, .faq__blockchain .acc-btn::after {
    display: none; }
  .faq__blockchain .acc-btn.active .arrow::after {
    opacity: 1; }
  .faq__blockchain .acc-btn.active .arrow span::before {
    opacity: 0; }
  .faq__blockchain .acc-btn.active .arrow span::after {
    opacity: 1; }
  .faq__blockchain .acc-btn span {
    margin: 0 !important; }
  .faq__blockchain .arrow {
    position: absolute;
    top: 0;
    right: 0;
    width: 40px;
    height: 40px;
    border-radius: 50px;
    background: #21232f;
    top: 50%;
    -webkit-transform: translateY(-50%);
    -ms-transform: translateY(-50%);
    transform: translateY(-50%); }
    .faq__blockchain .arrow span::before, .faq__blockchain .arrow span::after {
      position: absolute;
      top: 50%;
      left: 50%;
      content: "\f078";
      font-family: "Font Awesome 5 Pro";
      font-weight: 400;
      -webkit-transform: translate(-50%, -50%);
      -ms-transform: translate(-50%, -50%);
      transform: translate(-50%, -50%);
      font-size: 13px;
      color: #fff; }
    .faq__blockchain .arrow span::after {
      content: "\f077";
      opacity: 0;
      color: #03050E;
      z-index: 1;
      top: 48%; }
.faq__btc .accordion_box .block {
  background-color: #0B1121;
  border: 0 !important; }
  .faq__btc .accordion_box .block:not(:last-child) {
    margin-bottom: 20px; }
  .faq__btc .accordion_box .block .acc-btn {
    background-color: #111828;
    padding: 37px 40px;
    -webkit-transition: 0.3s;
    -o-transition: 0.3s;
    transition: 0.3s;
    font-size: 24px;
    font-weight: 400;
    letter-spacing: -0.48px; }
    @media (max-width: 767px) {
      .faq__btc .accordion_box .block .acc-btn {
        padding: 30px 20px;
        padding-right: 60px;
        font-size: 20px;
        line-height: 30px; } }
    .faq__btc .accordion_box .block .acc-btn::before, .faq__btc .accordion_box .block .acc-btn::after {
      display: none; }
  .faq__btc .accordion_box .block.active-block .acc-btn {
    background-color: #0B1121;
    padding-top: 30px;
    padding-bottom: 27px; }
    .faq__btc .accordion_box .block.active-block .acc-btn .arrow::before {
      opacity: 1; }
    .faq__btc .accordion_box .block.active-block .acc-btn .arrow span::before, .faq__btc .accordion_box .block.active-block .acc-btn .arrow span::after {
      color: #fff; }
    .faq__btc .accordion_box .block.active-block .acc-btn .arrow span::before {
      opacity: 0; }
    .faq__btc .accordion_box .block.active-block .acc-btn .arrow span::after {
      opacity: 1; }
  .faq__btc .accordion_box .block .arrow {
    position: absolute;
    top: 50%;
    right: 48px;
    -webkit-transform: translateY(-50%);
    -ms-transform: translateY(-50%);
    transform: translateY(-50%);
    width: 34px;
    height: 34px;
    background: #fff;
    margin: 0; }
    @media (max-width: 767px) {
      .faq__btc .accordion_box .block .arrow {
        right: 20px; } }
    .faq__btc .accordion_box .block .arrow::before {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: -1;
      content: "";
      opacity: 0;
      -webkit-transition: 0.3s;
      -o-transition: 0.3s;
      transition: 0.3s; }
    .faq__btc .accordion_box .block .arrow span::before, .faq__btc .accordion_box .block .arrow span::after {
      position: absolute;
      top: 50%;
      left: 50%;
      content: "\f078";
      font-family: "Font Awesome 5 Pro";
      font-weight: 400;
      -webkit-transform: translate(-50%, -50%);
      -ms-transform: translate(-50%, -50%);
      transform: translate(-50%, -50%);
      font-size: 13px;
      color: #0B1121; }
    .faq__btc .accordion_box .block .arrow span::after {
      content: "\f077";
      opacity: 0;
      color: #03050E;
      z-index: 1;
      top: 48%; }
  .faq__btc .accordion_box .block .content {
    border: 0;
    padding: 45px 40px;
    padding-top: 0;
    color: #A4BDCC;
    padding-right: 100px; }
    @media (max-width: 767px) {
      .faq__btc .accordion_box .block .content {
        padding: 30px 20px; } }
.faq__nav {
  border: 0; }
  @media (max-width: 767px) {
    .faq__nav {
      justify-content: start; } }
  .faq__nav li {
    margin-bottom: 20px; }
    .faq__nav li:not(:last-child) {
      margin-right: 50px; }
      @media (max-width: 991px) {
        .faq__nav li:not(:last-child) {
          margin-right: 35px; } }
    .faq__nav li .nav-link {
      font-size: 18px;
      font-weight: 600;
      color: #DAEDF8;
      padding: 0;
      border: 0; }
      .faq__nav li .nav-link.active {
        background-color: transparent; }

.accordion__wrap .accordion-title {
  margin-bottom: 30px; }

.accordion_box {
  position: relative; }

.accordion_box .block {
  position: relative;
  overflow: hidden; }
  .accordion_box .block:not(:last-child) {
    border-bottom: 1px solid #2B2B54; }

.accordion_box .block:last-child {
  margin-bottom: 0; }

.accordion_box .block .acc-btn {
  color: var(--color-white);
  position: relative;
  font-weight: 500;
  font-size: 26px;
  line-height: 34px;
  cursor: pointer;
  padding: 33px 0;
  padding-right: 20px; }
  @media (max-width: 767px) {
    .accordion_box .block .acc-btn {
      font-size: 18px;
      line-height: 32px; } }

.accordion_box .block .acc-btn span {
  display: block;
  font-size: 14px;
  color: #B3B3C0;
  font-weight: 600; }

.accordion_box .block .acc-btn::before,
.accordion_box .block .acc-btn::after {
  position: absolute;
  top: 53%;
  -webkit-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  transform: translateY(-50%);
  right: 0;
  border: 9px solid transparent;
  border-top: 14px solid rgba(255, 255, 255, 0.3);
  content: ""; }

.accordion_box .block .acc-btn::after {
  opacity: 1;
  border: 9px solid transparent;
  border-bottom: 14px solid var(--color-white);
  opacity: 0;
  top: 31%; }

.accordion_box .block.active-block .acc-btn::after {
  opacity: 1; }

.accordion_box .block.active-block .acc-btn::before {
  opacity: 0; }

.accordion_box .block .content {
  position: relative;
  padding-top: 32px;
  padding-bottom: 24px;
  border-top: 1px solid #2B2B54; }

.accordion_box .block .content p {
  font-size: 18px;
  color: #B3B3C0; }
  .accordion_box .block .content p:not(:last-child) {
    margin-bottom: 30px; }
  .accordion_box .block .content p a {
    color: var(--color-white);
    text-decoration: underline;
    font-weight: 500; }

.accordion_box .block .content ul {
  margin-bottom: 30px;
  padding-left: 11px;
  list-style: none; }
  .accordion_box .block .content ul li {
    font-size: 18px;
    color: #B3B3C0;
    position: relative;
    padding-left: 17px; }
    .accordion_box .block .content ul li::before {
      position: absolute;
      top: 50%;
      left: 0;
      -webkit-transform: translateY(-50%);
      -ms-transform: translateY(-50%);
      transform: translateY(-50%);
      width: 4px;
      height: 4px;
      border-radius: 50%;
      -webkit-border-radius: 50%;
      -moz-border-radius: 50%;
      -ms-border-radius: 50%;
      -o-border-radius: 50%;
      background-color: var(--color-white);
      content: ""; }
    .accordion_box .block .content ul li:not(:last-child) {
      margin-bottom: 5px;
      position: relative; }

.accordion_box .block .acc_body {
  position: relative;
  display: none; }

.accordion_box .block .acc_body.current {
  display: block; }

.faq__blockchain-two .accordion_box .block {
  border: none;
  border: 1px solid rgba(255, 255, 255, 0.28);
  border-radius: 20px;
  padding: 15px;
  backdrop-filter: blur(4.8000001907px);
  background: rgba(74, 33, 239, 0.18);
  margin-bottom: 10px; }
.faq__blockchain-two .accordion_box .accordion-inner {
  background: #0d0b33;
  border-radius: 20px;
  padding: 30px 39px 30px 30px; }
  @media (max-width: 767px) {
    .faq__blockchain-two .accordion_box .accordion-inner {
      padding: 28px 25px 28px 25px; } }
.faq__blockchain-two .accordion-inner .acc-btn {
  padding: 0; }
  .faq__blockchain-two .accordion-inner .acc-btn span:nth-child(1) {
    font-size: 26px;
    line-height: 34px;
    letter-spacing: -0.04px;
    color: var(--color-white);
    padding-right: 13px;
    display: inline-block; }
    @media (max-width: 767px) {
      .faq__blockchain-two .accordion-inner .acc-btn span:nth-child(1) {
        font-size: 18px; } }
  .faq__blockchain-two .accordion-inner .acc-btn::before {
    border-top: 14px solid var(--color-white); }
.faq__blockchain-two .accordion-inner .content {
  border: 0;
  padding-top: 10px;
  font-size: 20px;
  line-height: 32px;
  font-weight: 500;
  padding-left: 66px; }
  @media (max-width: 991px) {
    .faq__blockchain-two .accordion-inner .content {
      padding-left: 20px;
      padding-bottom: 0; } }
.faq__blockchain-two .accordion-inner .content .list-item {
  padding-top: 13px;
  margin-bottom: 11px; }

@media (max-width: 991px) {
  .faq-shape_two {
    display: none; } }
.faq-shape_two img {
  position: absolute; }
  .faq-shape_two img:nth-child(1) {
    top: 27%;
    left: 93px; }
  .faq-shape_two img:nth-child(2) {
    top: 46%;
    right: 54px; }
  .faq-shape_two img:nth-child(3) {
    bottom: -21px;
    left: 0; }
  .faq-shape_two img:nth-child(4) {
    right: 0;
    bottom: -30px; }

.xb-event {
  background: linear-gradient(335deg, #435fb2 0%, #111b5e 66.48%, #0c1449 100%);
  border-radius: 20px;
  border: 1px solid #6780D2; }
  .xb-event .xb-item--img {
    text-align: center;
    border-bottom: 1px solid #6780D2;
    border-radius: 20px 20px 0 0;
    overflow: hidden; }
    .xb-event .xb-item--img img {
      -webkit-transition: 0.4s;
      -o-transition: 0.4s;
      transition: 0.4s; }
  .xb-event .xb-item--holder {
    padding: 25px 30px 28px 30px; }
  .xb-event .xb-item--location {
    font-weight: 500; }
    .xb-event .xb-item--location span {
      padding-left: 12px; }
  .xb-event .xb-item--title {
    font-size: 22px;
    line-height: 34px;
    letter-spacing: -0.03px;
    padding: 15px 0; }
    @media only screen and (min-width: 1200px) and (max-width: 1300px) {
      .xb-event .xb-item--title {
        font-size: 18px;
        line-height: 30px; } }
    @media (max-width: 1199px) {
      .xb-event .xb-item--title {
        font-size: 16px;
        line-height: 28px;
        letter-spacing: 0; } }
    .xb-event .xb-item--title a {
      color: currentColor; }
  .xb-event .xb-item--date-time1 {
    display: flex;
    align-items: center; }
    .xb-event .xb-item--date-time1 span {
      padding-left: 8px; }
  .xb-event .xb-item--date {
    padding-right: 10px; }
  .xb-event .xb-item--event-btn {
    padding-top: 30px; }
.xb-event .xb-item--event-btn .them-btn {
  width: 100%;
  height: 50px;
  padding: 0;
  background: #0a0718;
  color: #fff;
  border: 1px solid #7bd6ff;
}
.xb-event .xb-item--event-btn .them-btn:hover {
  background-color: var(--color-primary);
  border-color: var(--color-primary);
  color: #0a0718;
}
.xb-event .xb-item--event-btn .them-btn .btn_icon svg path {
  fill: #fff;
}
.xb-event .xb-item--event-btn .them-btn:hover .btn_icon svg path {
  fill: #0a0718;
}
  .xb-event:hover .xb-item--img img {
    transform: scale(1.05); }

.xb-currency .xb-item--title {
  border-bottom: 1px solid #623bfd;
  padding-bottom: 15px; }
  @media (max-width: 1199px) {
    .xb-currency .xb-item--title {
      justify-content: space-between; } }
  .xb-currency .xb-item--title span {
    font-size: 18px;
    font-weight: 500;
    line-height: 24px; }
    @media (max-width: 767px) {
      .xb-currency .xb-item--title span {
        margin-bottom: 20px; } }
    .xb-currency .xb-item--title span:nth-child(1) {
      padding-right: 312px; }
      @media only screen and (min-width: 1200px) and (max-width: 1500px) {
        .xb-currency .xb-item--title span:nth-child(1) {
          padding-right: 250px; } }
      @media (max-width: 1199px) {
        .xb-currency .xb-item--title span:nth-child(1) {
          padding-right: 150px; } }
      @media (max-width: 991px) {
        .xb-currency .xb-item--title span:nth-child(1) {
          padding-right: 120px; } }
      @media (max-width: 767px) {
        .xb-currency .xb-item--title span:nth-child(1) {
          padding-right: 20px; } }
    .xb-currency .xb-item--title span:nth-child(2) {
      padding-right: 220px; }
      @media only screen and (min-width: 1200px) and (max-width: 1500px) {
        .xb-currency .xb-item--title span:nth-child(2) {
          padding-right: 160px; } }
      @media (max-width: 1199px) {
        .xb-currency .xb-item--title span:nth-child(2) {
          padding-right: 0; } }
      @media (max-width: 767px) {
        .xb-currency .xb-item--title span:nth-child(2) {
          padding-right: 20px; } }
    .xb-currency .xb-item--title span:nth-child(3) {
      padding-right: 165px; }
      @media only screen and (min-width: 1200px) and (max-width: 1500px) {
        .xb-currency .xb-item--title span:nth-child(3) {
          padding-right: 150px; } }
      @media (max-width: 1199px) {
        .xb-currency .xb-item--title span:nth-child(3) {
          padding-right: 0; } }
      @media (max-width: 767px) {
        .xb-currency .xb-item--title span:nth-child(3) {
          padding-right: 30px; } }
    .xb-currency .xb-item--title span:nth-child(4) {
      padding-right: 224px; }
      @media (max-width: 1199px) {
        .xb-currency .xb-item--title span:nth-child(4) {
          padding-right: 100px; } }
.xb-currency .xb-item--item {
  border-bottom: 1px solid #623bfd;
  padding-bottom: 30px;
  padding-top: 40px; }
  @media (max-width: 1199px) {
    .xb-currency .xb-item--item {
      justify-content: space-between; } }
  @media (max-width: 767px) {
    .xb-currency .xb-item--item li {
      margin-top: 30px; } }
  .xb-currency .xb-item--item li:nth-child(1) {
    width: 362px; }
    @media only screen and (min-width: 1200px) and (max-width: 1500px) {
      .xb-currency .xb-item--item li:nth-child(1) {
        width: 300px; } }
    @media (max-width: 1199px) {
      .xb-currency .xb-item--item li:nth-child(1) {
        width: auto; } }
  .xb-currency .xb-item--item li:nth-child(2) {
    width: 261px;
    font-size: 22px;
    font-weight: 500;
    line-height: 24px;
     }
    @media only screen and (min-width: 1200px) and (max-width: 1500px) {
      .xb-currency .xb-item--item li:nth-child(2) {
        width: 200px; } }
    @media (max-width: 1199px) {
      .xb-currency .xb-item--item li:nth-child(2) {
        width: auto; } }
  .xb-currency .xb-item--item li:nth-child(3) {
    width: 262px;
    font-size: 22px;
    font-weight: 500;
    line-height: 24px;
    }
    @media only screen and (min-width: 1200px) and (max-width: 1500px) {
      .xb-currency .xb-item--item li:nth-child(3) {
        width: 220px; } }
    @media (max-width: 1199px) {
      .xb-currency .xb-item--item li:nth-child(3) {
        width: auto; } }
  .xb-currency .xb-item--item li:nth-child(4) {
    width: 271px; }
    @media (max-width: 1199px) {
      .xb-currency .xb-item--item li:nth-child(4) {
        width: auto; } }
.xb-currency .xb-item--image {
  height: 50px;
  width: 50px;
  border-radius: 100%;
  display: flex;
  align-items: center;
  justify-content: center; }
  .xb-currency .xb-item--image.img-two {
    background: #627eea; }
  .xb-currency .xb-item--image.img-three {
    background: #259d76; }
  .xb-currency .xb-item--image.img-four {
    background: #23292f; }
  .xb-currency .xb-item--image.img-five {
    background: #2a5ada; }
.xb-currency .xb-item--color-white {
  width: 124px;
  font-size: 24px;
  font-weight: 500;
  line-height: 24px;
  margin-left: 20px; }
.xb-currency .xb-item--curr-btn {
  width: 134px;
  height: 50px;
  border: 1px solid #fff;
  background: transparent;
  color: var(--color-white);
  margin: 0;
  padding: 0;
}
.xb-currency .xb-item--curr-btn:hover {
  background: var(--color-primary);
  color: #080b18;
  border-color: var(--color-primary);
}

@media only screen and (min-width: 1200px) and (max-width: 1500px) {
  .currency-shape {
    display: none; } }
@media (max-width: 991px) {
  .currency-shape {
    display: none; } }
.currency-shape img {
  z-index: -1;
  position: absolute; }
  .currency-shape img:nth-child(1) {
    top: 38%;
    left: 32px; }
  .currency-shape img:nth-child(2) {
    top: 38%;
    right: 32px; }

.xb-review {
  border-top: 1px solid #2a284b;
  padding-bottom: 40px; }
  .xb-review .xb-item--holder {
    padding-top: 35px;
    padding-bottom: 14px; }
    .xb-review .xb-item--holder:not(:last-child) {
      border-right: 1px solid #2a284b;
      padding-right: 181px; }
      @media (max-width: 991px) {
        .xb-review .xb-item--holder:not(:last-child) {
          padding-right: 0;
          border: none; } }
  .xb-review .xb-item--review {
    padding-left: 20px; }
    .xb-review .xb-item--review span {
      font-weight: 500; }
  .xb-review .xb-item--star {
    display: flex;
    align-items: center;
    margin-bottom: -4px; }
  .xb-star li {
    font-size: 14px;
    color: #FCC640;
    margin-right: 6px;
  }
.xb-star li:last-child {
  margin-right: 0;
}
.xb-review .xb-item--star span {
      font-size: 18px;
      font-weight: 600;
      color: var(--color-white);
      padding-left: 8px; }

.xb-testimonial-wrap {
  align-items: start;
  padding: 65px 0 65px 0;
  background: #151246;
  border-radius: 20px;
  position: relative; }
  @media (max-width: 991px) {
    .xb-testimonial-wrap {
      padding: 40px 20px; } }
  .xb-testimonial-wrap::before {
    content: '';
    top: -1px;
    left: -1px;
    right: -1px;
    bottom: -1px;
    padding: 1px;
    height: 100%;
    position: absolute;
    border-radius: 20px;
    -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    mask-composite: add, add;
    mask-composite: add, add;
    -webkit-mask-composite: source-out;
    mask-composite: exclude;
    background: linear-gradient(162deg, #8f7bff 0%, #413886 28.35%, #0f0d39 73.81%, #2dd98f 100%); }
  .xb-testimonial-wrap .xb-item--author {
    width: 37.5%;
    padding-top: 15px; }
    @media (max-width: 991px) {
      .xb-testimonial-wrap .xb-item--author {
        width: 25%; } }
    @media (max-width: 767px) {
      .xb-testimonial-wrap .xb-item--author {
        width: 100%;
        margin-bottom: 20px; } }
  .xb-testimonial-wrap .xb-item--avater img {
    border-radius: 100%; }
  .xb-testimonial-wrap .xb-item--nationality {
    padding-top: 15px; }
    .xb-testimonial-wrap .xb-item--nationality span {
      padding-left: 8px; }
  .xb-testimonial-wrap .xb-item--title {
    font-size: 22px;
    line-height: 34px;
    letter-spacing: -0.03px;
    padding-top: 8px;
    padding-bottom: 4px; }
    @media (max-width: 991px) {
      .xb-testimonial-wrap .xb-item--title {
        font-size: 17px; } }
  .xb-testimonial-wrap .xb-item--content {
    width: calc(100% - 37.5%);
    position: relative;
    padding-left: 40px; }
    @media (max-width: 991px) {
      .xb-testimonial-wrap .xb-item--content {
        width: calc(100% - 25%);
        padding-left: 20px; } }
    @media (max-width: 767px) {
      .xb-testimonial-wrap .xb-item--content {
        width: 100%;
        padding-left: 0; } }
    .xb-testimonial-wrap .xb-item--content p {
      font-size: 26px;
      line-height: 36px;
      padding: 0 0 36px 60px;
      max-width: 690px; }
      @media (max-width: 991px) {
        .xb-testimonial-wrap .xb-item--content p {
          font-size: 20px; } }
      @media (max-width: 767px) {
        .xb-testimonial-wrap .xb-item--content p {
          font-size: 16px;
          line-height: 30px; } }
    .xb-testimonial-wrap .xb-item--content::before {
      content: '';
      height: 24px;
      width: 32px;
      position: absolute;
      background: url(../img/tes-qutation.png);
      top: 10px; }
  .xb-testimonial-wrap .xb-item--reating {
    padding-left: 60px; }
    .xb-testimonial-wrap .xb-item--reating span {
      font-size: 18px;
      font-weight: 600;
      padding: 0 6px 0 20px;
      color: #fff; }

.testimonial-two {
  background: #0d0b33;
  margin: 0 20px;
  border-radius: 20px;
  z-index: 1; }
  .testimonial-two::after, .testimonial-two::before {
    content: '';
    height: 673px;
    width: 512px;
    position: absolute;
    border-radius: 100%;
    background: #fff;
    filter: blur(400px);
    z-index: -1;
    top: 28%; }
  .testimonial-two::after {
    left: -135px; }
  .testimonial-two::before {
    right: -135px; }
  @media (max-width: 991px) {
    .testimonial-two .tes-shape {
      display: none; } }
  .testimonial-two .tes-shape .shape {
    position: absolute; }
    .testimonial-two .tes-shape .shape--2 {
      left: 35px;
      bottom: 0; }
    .testimonial-two .tes-shape .shape--3 {
      right: 30px;
      bottom: 0; }
    .testimonial-two .tes-shape .shape--4 {
      bottom: 29%;
      left: 43px; }
    .testimonial-two .tes-shape .shape--5 {
      bottom: 15%;
      right: 65px; }

.xb-integrate {
  margin: 0 -30px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative; }
  .xb-integrate .image {
    height: 160px;
    width: 160px;
    border-radius: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #0d0b33;
    position: absolute;
    top: 31.5%;
    left: 45.1%; }
    @media (max-width: 991px) {
      .xb-integrate .image {
        height: 100px;
        width: 100px;
        top: 26.5%;
        left: 43.1%; }
        .xb-integrate .image img {
          max-width: 50px; } }
    @media (max-width: 767px) {
      .xb-integrate .image {
        height: 60px;
        width: 60px;
        top: 25.5%;
        left: 42.1%; }
        .xb-integrate .image img {
          max-width: 30px; } }
    .xb-integrate .image::before {
      content: '';
      top: -1px;
      left: -1px;
      right: -1px;
      bottom: -1px;
      padding: 1px;
      height: 100%;
      position: absolute;
      border-radius: 100%;
      -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
      mask-composite: add, add;
      mask-composite: add, add;
      -webkit-mask-composite: source-out;
      mask-composite: exclude;
      background: linear-gradient(162deg, #8f7bff 0%, #413886 28.35%, #0f0d39 73.81%, #2dd98f 100%);
      animation: spin 5s infinite linear;
      -webkit-animation: spin 5s infinite linear; }

.roadmap-wrap .roadmap--item {
  position: relative;
  display: inline-block;
  z-index: 1; }
  .roadmap-wrap .roadmap--item::before {
    content: '';
    height: 233px;
    width: 1px;
    position: absolute;
    left: 48px;
    top: 10px;
    background: linear-gradient(181deg, #fff 0%, #000 100%); }
.roadmap-wrap .roadmap--head {
  font-size: 22px;
  line-height: 42px;
  padding-left: 70px;
  padding-bottom: 10px; }
.roadmap-wrap .roadmap--info {
  padding-left: 92px;
  padding-bottom: 67px; }
  .roadmap-wrap .roadmap--info li {
    list-style: 30px;
    text-transform: capitalize;
    padding-bottom: 5px; }
@media (max-width: 991px) {
  .roadmap-wrap .roadmap--year {
    display: none; } }
.roadmap-wrap .roadmap--year span {
  font-size: 14px;
  font-weight: 600;
  color: var(--color-primary);
  padding-top: 11px;
  display: block; }
.roadmap-wrap .roadmap--circle {
  height: 32px;
  width: 32px;
  border-radius: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 32px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  background: radial-gradient(208.8% 113.91% at 100% 100%, #ebf7fd 0%, #9162ff 9.0000003576%, #1c30a8 28.0000001192%, #080b18 75%); }
  @media (max-width: 991px) {
    .roadmap-wrap .roadmap--circle {
      display: none; } }
  .roadmap-wrap .roadmap--circle span {
    height: 12px;
    width: 12px;
    border-radius: 100%;
    box-shadow: 0 4px 11px 0 #1b2ff3;
    background: radial-gradient(532.71% 290.63% at 50% 90.63%, #ebf7fd 0%, #9162ff 9.0000003576%, #1c30a8 28.0000001192%, #080b18 75%); }
.roadmap-wrap .roadmap--line {
  width: 100%;
  height: 8px;
  background: #fff;
  opacity: 0.1;
  margin-top: -57px; }
  @media (max-width: 991px) {
    .roadmap-wrap .roadmap--line {
      display: none; } }
.roadmap-wrap .roadmap--top {
  display: flex;
  align-items: center;
  justify-content: space-around;
  padding-left: 200px;
  padding-right: 150px; }
  @media only screen and (min-width: 1200px) and (max-width: 1500px) {
    .roadmap-wrap .roadmap--top {
      padding-left: 100px; } }
  @media (max-width: 1199px) {
    .roadmap-wrap .roadmap--top {
      padding-left: 120px; } }
  @media (max-width: 991px) {
    .roadmap-wrap .roadmap--top {
      flex-wrap: wrap;
      padding: 0; } }
.roadmap-wrap .roadmap--bottom {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-left: 127px;
  padding-right: 40px; }
  @media only screen and (min-width: 1200px) and (max-width: 1500px) {
    .roadmap-wrap .roadmap--bottom {
      padding-left: 40px; } }
  @media (max-width: 1199px) {
    .roadmap-wrap .roadmap--bottom {
      padding-left: 11px; } }
  @media (max-width: 991px) {
    .roadmap-wrap .roadmap--bottom {
      flex-wrap: wrap;
      justify-content: space-around;
      margin-bottom: -70px; } }
  .roadmap-wrap .roadmap--bottom .bottom-item {
    display: flex;
    align-items: start;
    flex-direction: column;
    margin-top: -69px; }
    @media (max-width: 991px) {
      .roadmap-wrap .roadmap--bottom .bottom-item {
        margin-bottom: 90px; } }
    .roadmap-wrap .roadmap--bottom .bottom-item:nth-child(3) {
      margin-left: -29px; }
      @media (max-width: 991px) {
        .roadmap-wrap .roadmap--bottom .bottom-item:nth-child(3) {
          margin-left: 0; } }
    .roadmap-wrap .roadmap--bottom .bottom-item::before {
      background: linear-gradient(181deg, #000 0%, #fff 100%);
      top: 80px; }
      @media (max-width: 991px) {
        .roadmap-wrap .roadmap--bottom .bottom-item::before {
          top: 34px; } }
    .roadmap-wrap .roadmap--bottom .bottom-item .roadmap--head {
      padding-top: 68px; }
    .roadmap-wrap .roadmap--bottom .bottom-item .roadmap--info {
      padding-bottom: 0; }
    .roadmap-wrap .roadmap--bottom .bottom-item .roadmap--circle {
      margin-top: 11px; }

.section-title .title {
  font-weight: 700;
  font-size: 60px;
  line-height: 72px;
  letter-spacing: -0.02px;
  text-transform: capitalize;
  text-align: center; }
  @media (max-width: 1199px) {
    .section-title .title {
      font-size: 45px;
      letter-spacing: 0;
      line-height: 52.5px; } }
  @media (max-width: 991px) {
    .section-title .title {
      font-size: 35px;
      line-height: 45px; } }
  @media (max-width: 767px) {
    .section-title .title {
      font-size: 32px;
      letter-spacing: 0;
      line-height: 38.5px; } }

/*----------------------------------------*/
/*  03. globel
/*----------------------------------------*/
@media (min-width: 1024px) {
  .container, .container-lg, .container-md, .container-sm, .container-xl {
    max-width: 1320px;
    padding-left: 15px;
    padding-right: 15px; } }
.home-three {
  background-color: #020202; }
  .home-three main {
    background-color: #020202; }

.mxw_1660 {
  max-width: 1660px; }

.mxw_1820 {
  max-width: 1820px; }

.px-60 {
  padding: 0 60px; }

@media (max-width: 991px) {
  .container.px-60 {
    padding-left: 15px;
    padding-right: 15px; } }

.g-15 {
  padding: 0 5px; }
  .g-15 > * {
    padding-left: 8px;
    padding-right: 8px; }

.mt-12 {
  margin-top: 12px; }

.mt-none-12 {
  margin-top: -12px; }

.tagcloud, .tags-links {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  align-items: center;
  margin: -10px; }
  .tagcloud a, .tags-links a {
    display: block;
    color: #92939E;
    min-height: 32px;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
    font-size: 14px;
    text-transform: capitalize;
    text-decoration: none;
    font-weight: 500;
    padding: 1px 17px;
    margin: 7px;
    position: relative;
    background-color: #080B18;
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 3px;
    -webkit-border-radius: 3px;
    -moz-border-radius: 3px;
    -ms-border-radius: 3px;
    -o-border-radius: 3px; }
    .tagcloud a:hover, .tags-links a:hover {
      color: #080B18;
      background-color: var(--color-primary);
      border-color: var(--color-primary); }

.body-overlay {
  background-color: #000000;
  height: 100%;
  width: 100%;
  position: fixed;
  top: 0;
  z-index: 1010;
  left: 0;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s linear 0s;
  transition: all 600ms ease;
  -webkit-transition: all 600ms ease;
  -moz-transition: all 600ms ease;
  -ms-transition: all 600ms ease;
  -o-transition: all 600ms ease; }

.body-overlay.active {
  opacity: .5;
  visibility: visible; }

.post-tags li {
  font-size: 15px;
  text-transform: capitalize;
  position: relative; }
  .post-tags li:not(:first-child, :last-child) {
    margin-right: 11px;
    padding-right: 15px; }
    .post-tags li:not(:first-child, :last-child)::before {
      position: absolute;
      top: 50%;
      right: 0;
      -webkit-transform: translateY(-50%);
      -ms-transform: translateY(-50%);
      transform: translateY(-50%);
      width: 5px;
      height: 5px;
      background-color: var(--color-primary);
      border-radius: 50%;
      -webkit-border-radius: 50%;
      -moz-border-radius: 50%;
      -ms-border-radius: 50%;
      -o-border-radius: 50%;
      content: ""; }
  .post-tags li span {
    display: inline-block;
    background-color: var(--color-primary);
    padding: 0px 10px;
    line-height: 25px;
    color: var(--color-white);
    border-radius: 3px;
    -webkit-border-radius: 3px;
    -moz-border-radius: 3px;
    -ms-border-radius: 3px;
    -o-border-radius: 3px;
    margin-right: 12px; }
  .post-tags li a {
    color: var(--color-black); }
    .post-tags li a:hover {
      color: var(--color-black); }

.mfp-zoom-in .mfp-content {
  opacity: 0;
  -webkit-transition: all 0.4s ease;
  transition: all 0.4s ease;
  -webkit-transform: scale(0.9);
  -ms-transform: scale(0.9);
  transform: scale(0.9); }

.mfp-zoom-in.mfp-ready .mfp-content {
  opacity: 1;
  -webkit-transform: scale(1);
  -ms-transform: scale(1);
  transform: scale(1); }

body .mfp-wrap .mfp-container .mfp-content .mfp-close {
  padding: 0;
  right: 0;
  text-align: center;
  top: -36px;
  width: 36px;
  height: 36px;
  -webkit-border-radius: 0;
  -khtml-border-radius: 0;
  -moz-border-radius: 0;
  -ms-border-radius: 0;
  -o-border-radius: 0;
  border-radius: 0;
  background: var(--color-white);
  cursor: pointer;
  opacity: 1;
  font-size: 0;
  border: 9px solid transparent;
  position: absolute; }

body .mfp-wrap .mfp-container .mfp-content .mfp-figure .mfp-close {
  top: 4px; }

body .mfp-wrap .mfp-container .mfp-content .mfp-close::before, body .mfp-wrap .mfp-container .mfp-content .mfp-close::after {
  content: '';
  position: absolute;
  height: 2px;
  width: 100%;
  top: 50%;
  left: 0;
  margin-top: -1px;
  transform-origin: 50% 50%;
  -webkit-transition: all 0.25s cubic-bezier(0.645, 0.045, 0.355, 1);
  -khtml-transition: all 0.25s cubic-bezier(0.645, 0.045, 0.355, 1);
  -moz-transition: all 0.25s cubic-bezier(0.645, 0.045, 0.355, 1);
  -ms-transition: all 0.25s cubic-bezier(0.645, 0.045, 0.355, 1);
  -o-transition: all 0.25s cubic-bezier(0.645, 0.045, 0.355, 1);
  transition: all 0.25s cubic-bezier(0.645, 0.045, 0.355, 1);
  background-color: #222; }

body .mfp-wrap .mfp-container .mfp-content .mfp-close::before {
  -webkit-transform: rotate(45deg);
  -khtml-transform: rotate(45deg);
  -moz-transform: rotate(45deg);
  -ms-transform: rotate(45deg);
  -o-transform: rotate(45deg);
  transform: rotate(45deg); }

body .mfp-wrap .mfp-container .mfp-content .mfp-close::after {
  -webkit-transform: rotate(-45deg);
  -khtml-transform: rotate(-45deg);
  -moz-transform: rotate(-45deg);
  -ms-transform: rotate(-45deg);
  -o-transform: rotate(-45deg);
  transform: rotate(-45deg); }

body .mfp-wrap .mfp-container .mfp-content .mfp-close:hover::before, body .mfp-wrap .mfp-container .mfp-content .mfp-close:hover::after {
  -webkit-transform: rotate(0);
  -khtml-transform: rotate(0);
  -moz-transform: rotate(0);
  -ms-transform: rotate(0);
  -o-transform: rotate(0);
  transform: rotate(0); }

body .mfp-wrap .mfp-container .mfp-content .mfp-close:hover::before, body .mfp-wrap .mfp-container .mfp-content .mfp-close:hover::after {
  -webkit-transform: rotate(0);
  -khtml-transform: rotate(0);
  -moz-transform: rotate(0);
  -ms-transform: rotate(0);
  -o-transform: rotate(0);
  transform: rotate(0); }

.mfp-iframe-holder .mfp-content {
  max-width: 1170px; }

.xb-carousel-inner {
  margin: -30px; }

.xb-swiper-sliders {
  position: relative; }

.xb-swiper-container {
  overflow: hidden;
  padding: 30px; }

.xb-swiper-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
  z-index: 1;
  display: flex;
  -webkit-transition-property: -webkit-transform;
  transition-property: -webkit-transform;
  -o-transition-property: transform;
  transition-property: transform;
  transition-property: transform,-webkit-transform;
  -webkit-box-sizing: content-box;
  box-sizing: content-box; }

.xb-swiper-slide:not(.swiper-slide-visible) {
  opacity: 0; }

.xb-swiper-slide {
  -webkit-transition: opacity 0.25s cubic-bezier(0.71, 0.02, 0.31, 1);
  -khtml-transition: opacity 0.25s cubic-bezier(0.71, 0.02, 0.31, 1);
  -moz-transition: opacity 0.25s cubic-bezier(0.71, 0.02, 0.31, 1);
  -ms-transition: opacity 0.25s cubic-bezier(0.71, 0.02, 0.31, 1);
  -o-transition: opacity 0.25s cubic-bezier(0.71, 0.02, 0.31, 1);
  transition: opacity 0.25s cubic-bezier(0.71, 0.02, 0.31, 1);
  transform: translateZ(0);
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  display: flex;
  align-items: center;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  width: 100%;
  height: 100%;
  position: relative; }

.z-index-2 {
  z-index: 2;
  position: relative; }

.xb-overlay {
  display: flex;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%; }

.xb-overlay-link {
  z-index: 1; }

.text-12 {
  font-size: 12px; }

.letter-spacing-0 {
  letter-spacing: 0; }

@media (max-width: 991px) {
  .margin-none-md {
    margin-bottom: 0; } }

@media (max-width: 767px) {
  .mb-30-xs {
    margin-bottom: 30px; } }

.br-20 {
  border-radius: 20px;
  -webkit-border-radius: 20px;
  -moz-border-radius: 20px;
  -ms-border-radius: 20px;
  -o-border-radius: 20px; }

.z-3 {
  position: relative;
  z-index: 3; }

.ml-auto {
  margin-left: auto; }

.align-end {
  align-items: flex-end; }

.xb-hover-zoom:hover .xb-item--img img {
  -webkit-transform: scale(1.08);
  -ms-transform: scale(1.08);
  transform: scale(1.08); }
.xb-hover-zoom .xb-item--img {
  overflow: hidden; }
  .xb-hover-zoom .xb-item--img img {
    -webkit-transition: 0.4s;
    -o-transition: 0.4s;
    transition: 0.4s; }

.xb-law-swiper-arrow .xb-swiper-arrow {
  border: 1px solid rgba(255, 255, 255, 0.12);
  width: 60px;
  height: 60px;
  border-radius: 50%;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  -ms-border-radius: 50%;
  -o-border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 15px;
  color: var(--color-white);
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s; }
  .xb-law-swiper-arrow .xb-swiper-arrow.xb-swiper-arrow-next {
    margin-left: 10px; }
  .xb-law-swiper-arrow .xb-swiper-arrow:hover, .xb-law-swiper-arrow .xb-swiper-arrow.xb-swiper-arrow-next {
    color: var(--color-law);
    background-color: var(--color-white); }

.xb-law-swiper-arrow-main-color .xb-swiper-arrow {
  background-color: var(--color-law);
  color: var(--color-white); }

.pagination-style1 {
  bottom: -60px; }
  .pagination-style1 .swiper-pagination-bullet {
    width: 14px;
    height: 14px;
    background-color: #162432; }
  .pagination-style1.style-white .swiper-pagination-bullet {
    background-color: var(--color-white); }

.text-heading {
  color: var(--color-heading); }

.text-24 {
  font-size: 24px; }

.weight-medium {
  font-weight: 500; }

.xb-blur {
  background: var(--color-primary);
  filter: blur(139px);
  height: 481px;
  width: 481px;
  border-radius: 50%;
  z-index: -2; }
  @media (max-width: 991px) {
    .xb-blur {
      height: 300px;
      width: 300px; } }
  @media (max-width: 767px) {
    .xb-blur {
      height: 150px;
      width: 150px; } }

.social-icon li {
  margin-top: 10px; }
  .social-icon li:not(:last-child) {
    margin-right: 31px; }
    @media (max-width: 991px) {
      .social-icon li:not(:last-child) {
        margin-right: 20px; } }
  .social-icon li a {
    color: #FFFFFF;
    font-size: 24px;
    height: 64px;
    width: 64px;
    border-radius: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative; }
    @media (max-width: 767px) {
      .social-icon li a {
        font-size: 18px;
        height: 55px;
        width: 54px; } }
    .social-icon li a::before {
      position: absolute;
      top: 0;
      left: 0;
      bottom: 0;
      right: 0;
      content: '';
      z-index: -1;
      border-radius: 50%;
      -webkit-border-radius: 50%;
      -moz-border-radius: 50%;
      -ms-border-radius: 50%;
      -o-border-radius: 50%;
      -webkit-transition: all 0.4s cubic-bezier(0.56, 0.26, 0.31, 1.32);
      -o-transition: all 0.4s cubic-bezier(0.56, 0.26, 0.31, 1.32);
      transition: all 0.4s cubic-bezier(0.56, 0.26, 0.31, 1.32);
      transition-delay: .1s;
      transform-style: preserve-3d;
      -webkit-transform: scaleX(1);
      -ms-transform: scaleX(1);
      transform: scaleX(1);
      transform-origin: center;
      border: 1px solid rgba(255, 255, 255, 0.15); }
    .social-icon li a::after {
      position: absolute;
      top: 0;
      left: 0;
      bottom: 0;
      right: 0;
      content: '';
      z-index: -1;
      border-radius: 50%;
      -webkit-border-radius: 50%;
      -moz-border-radius: 50%;
      -ms-border-radius: 50%;
      -o-border-radius: 50%;
      -webkit-transition: all 0.4s cubic-bezier(0.56, 0.26, 0.31, 1.32);
      -o-transition: all 0.4s cubic-bezier(0.56, 0.26, 0.31, 1.32);
      transition: all 0.4s cubic-bezier(0.56, 0.26, 0.31, 1.32);
      background-color: var(--color-primary);
      transition-delay: .1s;
      -webkit-transform: scale(0);
      -ms-transform: scale(0);
      transform: scale(0);
      transform-style: preserve-3d;
      transform-origin: center; }
    .social-icon li a:hover {
      color: #020202; }
      .social-icon li a:hover::before {
        transform: scale(0); }
      .social-icon li a:hover::after {
        transform: scaleX(1); }

.row.g-150 {
  margin-left: -75px;
  margin-right: -75px; }
  @media only screen and (min-width: 1200px) and (max-width: 1500px) {
    .row.g-150 {
      margin-left: -35px;
      margin-right: -35px; } }
  @media (max-width: 1199px) {
    .row.g-150 {
      margin-left: -20px;
      margin-right: -20px; } }
  .row.g-150 > * {
    padding-left: 75px;
    padding-right: 75px; }
    @media only screen and (min-width: 1200px) and (max-width: 1500px) {
      .row.g-150 > * {
        padding-left: 35px;
        padding-right: 35px; } }
    @media (max-width: 1199px) {
      .row.g-150 > * {
        padding-left: 20px;
        padding-right: 20px; } }

.line_wrap {
  top: 0px;
  bottom: 0px;
  width: 100%;
  display: block;
  position: absolute;
  max-width: 1292px;
  margin: auto; }
  @media only screen and (min-width: 1200px) and (max-width: 1300px) {
    .line_wrap {
      max-width: 1171px; } }
  @media (max-width: 1199px) {
    .line_wrap {
      display: none; } }
  .line_wrap .xb-line::before, .line_wrap .xb-line::after {
    position: absolute;
    top: 0;
    left: -1px;
    width: 1px;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.18);
    content: "";
    z-index: 2; }
  .line_wrap .xb-line::after {
    left: auto;
    right: 1px; }

.xb-svg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1; }
  .xb-svg svg {
    display: block;
    position: absolute;
    width: 100%;
    height: 100%;
    overflow: visible; }

.hero {
  min-height: 1190px;
  background-position: center bottom; }
  @media (max-width: 991px) {
    .hero {
      padding-top: 100px; } }
  .hero--sec-titlt .title {
    font-size: 102px;
    line-height: 87px;
    letter-spacing: -0.03em; }
    @media only screen and (min-width: 1200px) and (max-width: 1500px) {
      .hero--sec-titlt .title {
        font-size: 90px; } }
    @media (max-width: 1199px) {
      .hero--sec-titlt .title {
        font-size: 60px;
        letter-spacing: 0; } }
    @media (max-width: 991px) {
      .hero--sec-titlt .title {
        font-size: 43px; } }
    @media (max-width: 767px) {
      .hero--sec-titlt .title {
        font-size: 30px;
        line-height: 46px; } }
  .hero__date-sec {
    text-align: center; }
    .hero__date-sec .xb-item--date {
      display: block; }
      .hero__date-sec .xb-item--date span {
        font-size: 12px;
        font-weight: 600;
        line-height: 21px;
        color: var(--color-heading);
        text-transform: uppercase;
        position: relative; }
        .hero__date-sec .xb-item--date span:not(:last-child) {
          padding-right: 94px; }
        .hero__date-sec .xb-item--date span:not(:last-child)::before {
          position: absolute;
          content: '';
          height: 1px;
          width: 40px;
          background: #d9d9d9;
          bottom: 0;
          right: 25px; }
    .hero__date-sec .xb-item--time {
      display: inline-block; }
      .hero__date-sec .xb-item--time span {
        font-weight: 700;
        font-family: var(--font-heading);
        font-size: 65px;
        line-height: 87px;
        letter-spacing: -0.02;
        color: var(--color-heading); }
        .hero__date-sec .xb-item--time span:not(:last-child) {
          padding-right: 18px; }
  .hero__btn {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-wrap: wrap; }
  .token-structure {
    background: linear-gradient(180deg, #080b18 0%, #202acf 100%);
    border: 1px solid #7bd6ff;
    border-radius: 20px; }
    @media (max-width: 991px) {
      .token-structure {
        margin-top: 100px; } }
    .token-structure .hero-token {
      padding: 7px 40px 40px;
      border-right: 1px solid #84d9ff; }
      @media (max-width: 991px) {
        .token-structure .hero-token {
          padding-bottom: 0; } }
      .token-structure .hero-token .xb-item--title {
        font-size: 30px;
        line-height: 87px;
        letter-spacing: -0.02;
        text-transform: capitalize;
        margin-bottom: -20px; }
      .token-structure .hero-token .xb-item--content {
        line-height: 24px;
        max-width: 406px;
        padding-bottom: 24px; }
      .token-structure .hero-token .xb-item--acc-title {
        font-family: var(--font-body);
        line-height: 26px;
        margin-bottom: 3px; }
      .token-structure .hero-token .xb-item--list li {
        margin-right: 20px; }
      .token-structure .hero-token .xb-item--list svg {
        margin-right: 8px;
        transform: translateY(-1px);
      }
.hero-scroll span {
    font-size: 18px;
    font-weight: 600;
    line-height: 30px;
    color: var(--color-white);
    text-transform: uppercase;
    padding-bottom: 20px;
    display: block; }
  @media (max-width: 767px) {
.hero-shape {
      display: none; } }
  .hero-shape .shape--1 {
    position: absolute;
    top: 11%;
    left: 7%; }
    @media only screen and (min-width: 1200px) and (max-width: 1500px) {
      .hero-shape .shape--1 {
        left: 0%;
        max-width: 60px; } }
    @media (max-width: 1199px) {
      .hero-shape .shape--1 {
        left: 0;
        max-width: 70px; } }
    @media (max-width: 991px) {
      .hero-shape .shape--1 {
        max-width: 50px; } }
  .hero-shape .shape--2 {
    position: absolute;
    left: 13%;
    top: 29%; }
    @media only screen and (min-width: 1200px) and (max-width: 1500px) {
      .hero-shape .shape--2 {
        left: 5%;
        top: 29%;
        max-width: 100px; } }
    @media (max-width: 1199px) {
      .hero-shape .shape--2 {
        left: 1%;
        top: 31%;
        max-width: 70px; } }
    @media (max-width: 991px) {
      .hero-shape .shape--2 {
        max-width: 50px; } }
 .hero-shape .shape--3 {
    position: absolute;
    right: 14%;
    top: 27.5%; }
    @media only screen and (min-width: 1200px) and (max-width: 1500px) {
      .hero-shape .shape--3 {
        right: 8%;
        max-width: 100px; } }
    @media (max-width: 1199px) {
      .hero-shape .shape--3 {
        right: 2%;
        max-width: 80px; } }
    @media (max-width: 991px) {
      .hero-shape .shape--3 {
        max-width: 50px; } }
  .hero-shape .shape--4 {
    position: absolute;
    top: 7%;
    right: 4%; }
    @media only screen and (min-width: 1200px) and (max-width: 1500px) {
      .hero-shape .shape--4 {
        right: 2%;
        max-width: 60px; } }
    @media (max-width: 1199px) {
      .hero-shape .shape--4 {
        right: 2%;
        max-width: 71px; } }
    @media (max-width: 991px) {
      .hero-shape .shape--4 {
        max-width: 50px; } }
  .hero-shape .shape--5 {
    position: absolute;
    bottom: 38px;
    left: 50px; }
    @media (max-width: 991px) {
      .hero-shape .shape--5 {
        max-width: 70px; } }
  .hero-shape .shape--6 {
    position: absolute;
    bottom: 38px;
    right: 50px; }
.hero-sale {
    padding: 46px 50px 45px 25px; }
    .hero-sale .xb-item--sale_service span {
      font-weight: 500;
      line-height: 30px;
      text-transform: uppercase; }
    .hero-sale .xb-item--line {
      max-width: 90%;
      margin-left: 40px; }
      .hero-sale .xb-item--line span {
        width: 1px;
        height: 43px;
        background: linear-gradient(212deg, #0f144e 26.47%, #fff 64.06%, #121969 100%); }
    .hero-sale .xb-item--progress {
      border-radius: 20px;
      width: 100%;
      height: 8px;
      background: rgba(255, 255, 255, 0.3); }
    .hero-sale .xb-item--pro-color {
      border-radius: 20px;
      width: 75%;
      height: 8px;
      background: var(--color-primary); }
      .hero-sale .xb-item--pro-color .shape {
        height: 30px;
        width: 8px;
        background: var(--color-primary);
        float: right;
        margin-top: -11px; }
    .hero-sale .xb-item--target {
      padding-top: 27px;
      width: 75%; }
      @media (max-width: 1199px) {
        .hero-sale .xb-item--target {
          width: 100%; } }
      .hero-sale .xb-item--target span {
        font-size: 20px;
        font-weight: 600;
        line-height: 30px; }

@media (max-width: 991px) {
  .countdown {
    margin-top: 40px; } }
.countdown .single {
  text-align: center;
  min-width: 85px; }
  .countdown .single:not(:last-child) {
    margin-right: 60px; }
    @media (max-width: 767px) {
      .countdown .single:not(:last-child) {
        margin-right: 30px;
        min-width: 30px; } }
  .countdown .single:last-child h1:before {
    display: none; }
  .countdown .single p {
    font-size: 12px;
    font-weight: 600;
    line-height: 21px;
    color: var(--color-heading);
    text-transform: uppercase;
    position: relative;
    text-align: center;
    margin-bottom: 15px; }
  .countdown .single h1 {
    font-weight: 700;
    font-family: var(--font-heading);
    font-size: 65px;
    line-height: 87px;
    letter-spacing: -0.02;
    color: var(--color-heading);
    position: relative; }
    @media (max-width: 991px) {
      .countdown .single h1 {
        font-size: 50px; } }
    @media (max-width: 767px) {
      .countdown .single h1 {
        font-size: 30px; } }
    .countdown .single h1::before {
      position: absolute;
      top: 45%;
      right: -40px;
      -webkit-transform: translateY(-50%);
      -ms-transform: translateY(-50%);
      transform: translateY(-50%);
      content: ":"; }
      @media (max-width: 767px) {
        .countdown .single h1::before {
          right: -25px; } }

.scroll-down {
  position: relative;
  width: 30px;
  height: 26px; }

.hero-scroll {
  display: block;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column; }
  @media (max-width: 1199px) {
    .hero-scroll {
      padding-top: 55px; } }
  @media (max-width: 991px) {
    .hero-scroll {
      padding-top: 30px; } }

.chevron {
  position: absolute;
  width: 34px;
  height: 4px;
  opacity: 0;
  transform: scale3d(0.5, 0.5, 0.5);
  animation: move 3s ease-out infinite; }

.chevron:first-child {
  animation: move 3s ease-out 1s infinite; }

.chevron:nth-child(2) {
  animation: move 3s ease-out 2s infinite; }

.chevron:before,
.chevron:after {
  content: ' ';
  position: absolute;
  top: 0;
  height: 100%;
  width: 51%;
  background: #fff; }

.chevron:before {
  left: 0;
  transform: skew(0deg, 30deg); }

.chevron:after {
  right: 0;
  width: 50%;
  transform: skew(0deg, -30deg); }

@keyframes move {
  25% {
    opacity: 1; }
  33% {
    opacity: 1;
    transform: translateY(35px); }
  67% {
    opacity: 1;
    transform: translateY(50px); }
  100% {
    opacity: 0;
    transform: translateY(65px) scale3d(0.5, 0.5, 0.5); } }
.text {
  display: block;
  margin-top: 75px;
  margin-left: -30px;
  font-family: "Helvetica Neue", "Helvetica", Arial, sans-serif;
  font-size: 12px;
  color: #fff;
  text-transform: uppercase;
  white-space: nowrap;
  opacity: .25;
  animation: pulse 2s linear alternate infinite; }

@keyframes pulse {
  to {
    opacity: 1; } }
.hero-two {
  position: relative;
  z-index: 1; }
  @media (max-width: 991px) {
    .hero-two {
      margin-bottom: 0; } }
  @media (max-width: 767px) {
    .hero-two {
      min-height: 930px;
      padding-top: 80px; } }

.hero-style-two {
  padding-top: 26px;
  min-height: 1050px; }
  @media (max-width: 767px) {
    .hero-style-two {
      padding-top: 30px; } }
    .hero--sec-titlt-two .title {
      font-size: 90px;
      line-height: 110px;
      text-align: center;
      display: inline-block;
      letter-spacing: -0.02em; }
      @media only screen and (min-width: 1200px) and (max-width: 1500px) {
        .hero--sec-titlt-two .title {
          font-size: 80px;
          line-height: 96px; } }
      @media (max-width: 1199px) {
        .hero--sec-titlt-two .title {
          font-size: 60px;
          line-height: 75px; } }
      @media (max-width: 767px) {
        .hero--sec-titlt-two .title {
          font-size: 32px;
          line-height: 40px; } }
      .hero--sec-titlt-two .title span {
        position: relative;
        z-index: 1;
        padding: 0 9px; }
        .hero--sec-titlt-two .title span::before {
          content: '';
          position: absolute;
          height: 75%;
          width: 100%;
          top: 61%;
          transform: translateY(-50%);
          background: linear-gradient(92deg, #1d7361 0%, #0d0b33 100%);
          left: 0;
          z-index: -1;
          display: inline-block; }
        .hero--sec-titlt-two .title span::after {
          position: absolute;
          content: '';
          height: 75%;
          width: 2px;
          top: 61%;
          transform: translateY(-50%);
          background: linear-gradient(270deg, #0d0b33 0%, #185253 100%);
          left: 0;
          z-index: -1; }
  .hero-style-two .xb-item--content {
    font-size: 24px;
    line-height: 34px;
    max-width: 649px;
    text-align: center;
    display: inline-block; }
    @media (max-width: 767px) {
      .hero-style-two .xb-item--content {
        font-size: 20px;
        line-height: 30px; } }
  .hero-style-two .hero__btn .them-btn {
    border: 1px solid #fff; }
    .hero-style-two .hero__btn .them-btn:hover {
      border-color: var(--color-primary); }
  .hero-style-two .hero__btn .them-active {
    border: none; }
  .hero-dashbord {
    position: relative;
    text-align: center;
  }
  @media (max-width: 1199px) {
    .hero-style-two {
      min-height: 900px; } }
  @media (max-width: 991px) {
    .hero-style-two {
      padding-top: 40px;
      min-height: 700px; } }
  @media (max-width: 767px) {
    .hero-style-two {
      padding-bottom: 83px;
      padding-top: 60px; } }

@media (max-width: 991px) {
  .hero-shape-two {
    display: none; } }
.hero-shape-two .shape--1 {
  position: absolute;
  left: 139px;
  top: 22%; }
  @media only screen and (min-width: 1200px) and (max-width: 1500px) {
    .hero-shape-two .shape--1 {
      left: 15px;
      top: 22%;
      max-width: 100px; } }
  @media (max-width: 1199px) {
    .hero-shape-two .shape--1 {
      left: 10px;
      top: 22%;
      max-width: 100px; } }
.hero-shape-two .shape--2 {
  position: absolute;
  right: 104px;
  top: 21%; }
  @media only screen and (min-width: 1200px) and (max-width: 1500px) {
    .hero-shape-two .shape--2 {
      right: 0;
      max-width: 200px; } }
  @media (max-width: 1199px) {
    .hero-shape-two .shape--2 {
      right: 10px;
      max-width: 160px; } }
.hero-shape-two .shape--3 {
  position: absolute;
  left: 20%;
  bottom: 17%; }
.hero-shape-two .shape--4 {
  position: absolute;
  right: 20%;
  bottom: 17%; }
.hero-shape-two .shape--5 {
  position: absolute;
  right: 0;
  bottom: 0; }
.hero-shape-two .shape--6 {
  position: absolute;
  left: 0;
  bottom: 0; }

@media (max-width: 991px) {
  .xb-hero-shape {
    display: none; } }
.xb-hero-shape .shape {
  position: absolute; }
  .xb-hero-shape .shape--1 {
    bottom: 10px;
    left: 6%; }
  .xb-hero-shape .shape--2 {
    bottom: -64px;
    left: 4%; }
  .xb-hero-shape .shape--3 {
    bottom: -19%;
    right: 4%; }
  .xb-hero-shape .shape--4 {
    width: 5px;
    height: 5px;
    background-color: var(--color-white);
    border-radius: 50%;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -ms-border-radius: 50%;
    -o-border-radius: 50%;
    bottom: -22%;
    right: 8%;
    animation: startFadeIn 1.2s infinite cubic-bezier(0, 0, 0.2, 1); }
  .fadeinup {
    animation: startFadeIn 1.2s infinite cubic-bezier(0, 0, 0.2, 1);
  }

.dashbord-shape .shape {
  position: absolute;
  z-index: -1; }
  .dashbord-shape .shape--1 {
    top: 29%;
    left: 21%; }
  .dashbord-shape .shape--2 {
    top: 29%;
    right: 20%; }

.hero-img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center center; }
  @media (max-width: 991px) {
    .hero-img {
      top: -10%; } }

.hero-overlay-shape .shape {
  position: absolute;
  z-index: -1;
  bottom: -100%; }
  @media only screen and (min-width: 1200px) and (max-width: 1500px) {
    .hero-overlay-shape .shape {
      bottom: -50%; } }
  @media (max-width: 1199px) {
    .hero-overlay-shape .shape {
      bottom: -40%; } }
  @media (max-width: 991px) {
    .hero-overlay-shape .shape {
      bottom: -20%; } }
  .hero-overlay-shape .shape--1 {
    left: 0; }
  .hero-overlay-shape .shape--2 {
    right: -10%; }
  .btn-link-inner {
    display: flex;
  }
.them-btn {
  line-height: 1;
  padding: 0 88px;
  font-size: 18px;
  overflow: hidden;
  font-weight: 700;
  -webkit-transition: 300ms;
  transition: 300ms;
  position: relative;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  letter-spacing: 1px;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  color: #080b18;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  background-color: var(--color-primary);
  border: 1px solid transparent;
  border-radius: 50px;
  -webkit-border-radius: 50px;
  -moz-border-radius: 50px;
  -ms-border-radius: 50px;
  -o-border-radius: 50px; 
}
@media (max-width: 991px) {
  .them-btn {
    padding: 0 60px;
  } 
}
.them-btn.btn-transparent {
  background-color: transparent;
  color: #fff;
  border-color: #7bd6ff; }
.them-btn.btn-transparent:hover {
  background-color: var(--color-primary);
  border-color: var(--color-primary);
  color: #080b18; }
.them-btn.btn-transparent:hover .btn_icon svg path {
  fill: #080b18; }
.them-btn.btn-transparent .btn_icon svg path {
  fill: #fff; }

.them-btn .btn_icon {
  line-height: 1;
  font-size: 16px;
  margin-left: 20px; }
.them-btn .btn_icon svg path,
.footer-apps .xb-item--button .apps-btn span svg path{
  fill: #080b18;
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s; }

.them-btn .btn_label {
  position: relative;
  display: inline-block;
  padding: 27px 0px 25px;
  -webkit-transition: -webkit-transform 0.4s cubic-bezier(0.15, 0.85, 0.31, 1);
  transition: -webkit-transform 0.4s cubic-bezier(0.15, 0.85, 0.31, 1);
  transition: transform 0.4s cubic-bezier(0.15, 0.85, 0.31, 1);
  transition: transform 0.4s cubic-bezier(0.15, 0.85, 0.31, 1), -webkit-transform 0.4s cubic-bezier(0.15, 0.85, 0.31, 1); }

.them-btn .btn_label:before {
  top: 120%;
  left: 50%;
  width: 100%;
  display: block;
  position: absolute;
  content: attr(data-text);
  -webkit-transform: translateX(-50%);
  transform: translateX(-50%); }

.them-btn:hover {
  -webkit-transform: translateY(-2px);
  transform: translateY(-2px);
  color: #080b18; }

.them-btn:hover .btn_label {
  -webkit-transform: translateY(-81%);
  transform: translateY(-81%); }

.btns {
  margin: -15px; }
.btns a {
  margin: 15px; }

/*----------------------------------------*/
/*  27. backtotop
/*----------------------------------------*/
.xb-backtotop {
  right: 30px;
  z-index: 999;
  bottom: 20px;
  position: fixed;
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
  opacity: 0;
  visibility: hidden; }
  .xb-backtotop.active {
    bottom: 40px;
    opacity: 1;
    visibility: visible; }
  .xb-backtotop .scroll {
    z-index: 1;
    width: 40px;
    height: 40px;
    display: block;
    background-color: var(--color-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    color: var(--color-black);
    border-radius: 5px;
    -webkit-border-radius: 5px;
    -moz-border-radius: 5px;
    -ms-border-radius: 5px;
    -o-border-radius: 5px;
    font-size: 14px; }
    .xb-backtotop .scroll:hover {
      margin-bottom: 4px; }

/*----------------------------------------*/
/*  24. breadcrumb
/*----------------------------------------*/
.breadcrumb {
  min-height: 400px;
  padding: 120px 0;
  margin: 0;
  display: flex;
  align-items: center;
  background: linear-gradient(180deg, #090e26 0%, #10255d 20.42%, #1983d0 78.36%, #1baff7 100%);
  z-index: 1; }
  @media only screen and (min-width: 1200px) and (max-width: 1500px) {
    .breadcrumb {
      background-position-y: 200%; } }
  @media (max-width: 991px) {
    .breadcrumb {
      min-height: 200px;
      padding: 80px 0;
      background-position-y: 300%; } }
  .breadcrumb__title {
    text-align: center;
    color: #fff;
    font-size: 60px;
    margin-bottom: 28px; }
    @media (max-width: 991px) {
      .breadcrumb__title {
        font-size: 46px; } }
    @media (max-width: 767px) {
      .breadcrumb__title {
        font-size: 32px; } }
  .breadcrumb .bread-crumb .breadcrumb-item {
    color: var(--color-white);
    font-size: 16px;
    font-weight: 600; }
    .breadcrumb .bread-crumb .breadcrumb-item:not(:first-child) {
      padding-left: 5px; }
      .breadcrumb .bread-crumb .breadcrumb-item:not(:first-child)::before {
        content: "-";
        left: 2px;
        position: relative;
        color: var(--color-default); }
    .breadcrumb .bread-crumb .breadcrumb-item a {
      color: var(--color-default);
      font-weight: 500; }
      .breadcrumb .bread-crumb .breadcrumb-item a:hover {
        color: var(--color-white); }
  @media (max-width: 991px) {
    .breadcrumb__icon {
      display: none; } }
  .breadcrumb__icon .icon {
    position: absolute;
    z-index: -1; }
    .breadcrumb__icon .icon--1 {
      left: 8.6%;
      top: 20%; }
      @media (max-width: 991px) {
        .breadcrumb__icon .icon--1 {
          left: 0;
          top: 10%; } }
    .breadcrumb__icon .icon--2 {
      left: 2.8%;
      bottom: 5%; }
      @media (max-width: 991px) {
        .breadcrumb__icon .icon--2 {
          bottom: 0%; } }
    .breadcrumb__icon .icon--3 {
      right: 11%;
      top: 18%; }
      @media (max-width: 991px) {
        .breadcrumb__icon .icon--3 {
          right: 0;
          top: 34%; } }
    .breadcrumb__icon .icon--4 {
      right: 0;
      bottom: 0; }

.breadcrumb-container span {
  margin: 0 4px;
}
.breadcrumb-container a {
  color: var(--color-default);
  font-weight: 500;
}
.breadcrumb-container a:hover {
  color: #fff;
}
.breadcrumb-container .current-item {
  color: #fff;
  font-width: 600;
}

/*----------------------------------------*/
/*  13. blog
/*----------------------------------------*/
.blog__item {
  position: relative;
  z-index: 2;
  background-color: #0C1449;
  border-radius: 20px;
  -webkit-border-radius: 20px;
  -moz-border-radius: 20px;
  -ms-border-radius: 20px;
  -o-border-radius: 20px;
  overflow: hidden; }
  .blog__item .thumb {
    overflow: hidden;
    display: inline-block;
    margin-bottom: -8px; }
    .blog__item .thumb img {
      -webkit-transition: 0.3s;
      -o-transition: 0.3s;
      transition: 0.3s; }
  .blog__item .title {
    font-size: 32px;
    line-height: 42px;
    margin-bottom: 18px; }
    @media (max-width: 767px) {
      .blog__item .title {
        font-size: 22px;
        line-height: 34px; } }
    .blog__item .title a {
      color: var(--color-white); }
  .blog__item p {
    margin-bottom: 38px;
    font-size: 18px;
    line-height: 30px; }
  .blog__item .thm-btn {
    padding: 14px 38px; }
  .blog__item:hover .thumb img {
    -webkit-transform: scale(1.05);
    -ms-transform: scale(1.05);
    transform: scale(1.05); }
.blog__inner {
  background-image: url(../img/post_shape.png);
  background-size: cover;
  background-position: center center;
  background-repeat: no-repeat; }
@media (max-width: 767px) {
  .blog__meta {
    margin-bottom: 20px; } }
.blog__meta li {
  color: #B3B3C0;
  margin-bottom: 10px; }
  .blog__meta li:not(:last-child) {
    margin-right: 45px; }
    @media (max-width: 767px) {
      .blog__meta li:not(:last-child) {
        margin-right: 20px; } }
  .blog__meta li a {
    color: inherit; }
  .blog__meta li i {
    color: var(--color-primary);
    margin-right: 6px;
    font-size: 14px; }
.blog__inner {
  padding: 40px;
  padding-top: 35px; }
  @media (max-width: 767px) {
    .blog__inner {
      padding: 20px; } }
.blog__shape .shape {
  position: absolute;
  z-index: -1; }
  .blog__shape .shape--1 {
    top: 1%;
    right: 0; }
  .blog__shape .shape--2 {
    top: 13%;
    left: -2%; }
  .blog__shape .shape--3 {
    top: 27%;
    right: 0; }
  .blog__shape .shape--4 {
    top: 36%;
    right: 0; }
  .blog__shape .shape--5 {
    top: 47%;
    left: -4%; }

.widget {
  padding: 50px 40px;
  background-color: #0C1449;
  border-radius: 20px;
  -webkit-border-radius: 20px;
  -moz-border-radius: 20px;
  -ms-border-radius: 20px;
  -o-border-radius: 20px;
  z-index: 2;
  position: relative;
  background-image: url(../img/widget_bg.png);
  background-size: cover;
  background-position: center center;
  background-repeat: no-repeat; }
  @media (max-width: 767px) {
    .widget {
      padding: 40px 29px; } }
  .widget::before {
    content: '';
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    padding: 1px;
    position: absolute;
    border-radius: 20px;
    -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    mask-composite: add, add;
    mask-composite: add, add;
    mask-composite: add, add;
    mask-composite: add, add;
    -webkit-mask-composite: source-out;
    mask-composite: exclude;
    background: linear-gradient(334deg, #6780d2 0%, #2f3b8d 100%);
    z-index: -1;
    opacity: .8; }
  .widget__title {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 30px; }
  .widget__search {
    position: relative; }
    .widget__search input {
      background-color: #080B18;
      height: 60px;
      border-radius: 5px;
      -webkit-border-radius: 5px;
      -moz-border-radius: 5px;
      -ms-border-radius: 5px;
      -o-border-radius: 5px;
      font-size: 18px; }
      .widget__search input::-webkit-input-placeholder {
        color: #92939E;
        opacity: 1; }
      .widget__search input::-moz-placeholder {
        color: #92939E;
        opacity: 1; }
      .widget__search input:-ms-input-placeholder {
        color: #92939E;
        opacity: 1; }
      .widget__search input:-moz-placeholder {
        color: #92939E;
        opacity: 1; }
    .widget__search button {
      position: absolute;
      top: 50%;
      right: 20px;
      color: #92939E;
      -webkit-transform: translateY(-50%);
      -ms-transform: translateY(-50%);
      transform: translateY(-50%);
      background-color: transparent;
      font-size: 17px; }
  .widget__category li:not(:last-child) {
    margin-bottom: 23px; }
  .widget__category li a {
    font-size: 18px;
    color: #B3B3C0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-weight: 500; }
    .widget__category li a:hover {
      color: var(--color-white); }
  .widget__post-item:not(:last-child) {
    margin-bottom: 30px; }
  .widget__post-item .post-thumb {
    width: 80px;
    height: 80px;
    margin-right: 20px;
    border-radius: 5px;
    -webkit-border-radius: 5px;
    -moz-border-radius: 5px;
    -ms-border-radius: 5px;
    -o-border-radius: 5px;
    overflow: hidden; }
    .widget__post-item .post-thumb img {
      height: 100%;
      width: 100%;
      object-fit: cover; }
  .widget__post-item .post-content {
    width: calc(100% - 100px); }
  .widget__post-item .post-date {
    font-size: 14px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 1.4px;
    color: #B3B3C0; }
  .widget__post-item .post-title {
    font-size: 18px;
    font-weight: 400;
    color: var(--color-white);
    line-height: 26px;
    font-family: var(--font-body); }
    .widget__post-item .post-title a {
      color: inherit; }
  .widget__social li:not(:last-child) {
    margin-right: 10px; }
  .widget__social li a {
    width: 55px;
    height: 55px;
    border-radius: 50%;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -ms-border-radius: 50%;
    -o-border-radius: 50%;
    color: var(--color-white);
    background-color: #0B0B29;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px; }
    .widget__social li a:hover {
      color: #080B18;
      background-color: var(--color-primary); }
  .widget__divider {
    width: 100%;
    height: 1px;
    background-color: #141e62; }

.post-details .post-thumb {
  border-radius: 20px;
  -webkit-border-radius: 20px;
  -moz-border-radius: 20px;
  -ms-border-radius: 20px;
  -o-border-radius: 20px;
  overflow: hidden;
  margin-bottom: 30px; }
.post-details h2 {
  font-size: 32px;
  font-weight: 700;
  margin-bottom: 30px; }
  @media (max-width: 767px) {
    .post-details h2 {
      font-size: 24px; } }
.post-details h3 {
  font-size: 28px;
  font-weight: 700;
  margin-bottom: 25px; }
  @media (max-width: 767px) {
    .post-details h3 {
      font-size: 22px; } }
.post-details p {
  font-size: 18px;
  line-height: 30px; }
  .post-details p:not(:last-child) {
    margin-bottom: 30px; }

blockquote {
  background-color: #0B1348;
  background-repeat: no-repeat;
  background-size: cover;
  padding: 40px 55px;
  padding-left: 130px;
  border-radius: 20px;
  -webkit-border-radius: 20px;
  -moz-border-radius: 20px;
  -ms-border-radius: 20px;
  -o-border-radius: 20px;
  position: relative;
  font-family: var(--font-heading);
  margin: 40px 0;
  background-image: url(../img/quote_bg.png);
  background-position: center center;
  background-size: cover;
  background-repeat: no-repeat;
  position: relative;
  z-index: 1; }
  blockquote::before {
    content: '';
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    padding: 1px;
    position: absolute;
    border-bottom-right-radius: 20px;
    border-top-right-radius: 20px;
    -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    mask-composite: add, add;
    mask-composite: add, add;
    mask-composite: add, add;
    mask-composite: add, add;
    mask-composite: add, add;
    -webkit-mask-composite: source-out;
    mask-composite: exclude;
    background: linear-gradient(334deg, #6780d2 0%, #2f3b8d 100%);
    z-index: -1;
    opacity: .4; }
  @media (max-width: 767px) {
    blockquote {
      padding: 30px 55px;
      padding-left: 55px;
      padding-left: 100px; } }
  blockquote p {
    font-size: 20px;
    font-weight: 600;
    color: #fff;
    line-height: 34px;
    margin-bottom: 20px !important; }
  blockquote span {
    font-size: 18px;
    font-weight: 500;
    color: var(--color-default);
    padding-left: 27px;
    position: relative; }
    blockquote span::before {
      position: absolute;
      top: 50%;
      -webkit-transform: translateY(-50%);
      -ms-transform: translateY(-50%);
      transform: translateY(-50%);
      left: 0;
      width: 17px;
      height: 1px;
      background-color: var(--color-default);
      content: ""; }
  blockquote .quote {
    position: absolute;
    top: 0;
    left: 0;
    width: 80px;
    height: 80px;
    background-color: var(--color-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 20px 0;
    -webkit-border-radius: 20px 0;
    -moz-border-radius: 20px 0;
    -ms-border-radius: 20px 0;
    -o-border-radius: 20px 0; }

.post-info-list {
  padding-left: 5; }
  @media (max-width: 1199px) {
    .post-info-list {
      padding-left: 0; } }
  .post-info-list li {
    font-size: 18px; }
    .post-info-list li:not(:last-child) {
      margin-bottom: 18px; }
    .post-info-list li img {
      margin-right: 12px; }

.post-tags .tags-links {
  margin: 0;}
.post-tags .tag-title {
  font-size: 20px;
  margin-right: 15px;
  font-weight: 600; }

.social-share .post-share {
  margin-top: 20px; }
.social-share li:not(:last-child) {
  margin-right: 20px; }
.social-share li a {
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: #92939E;
  font-size: 14px;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  -ms-border-radius: 50%;
  -o-border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center; }
  .social-share li a:hover {
    background-color: var(--color-primary);
    border-color: var(--color-primary);
    color: #080B18; }
.social-share .title {
  font-size: 20px;
  margin-right: 15px;
  font-weight: 600; }

/* comment css */
.post-comments {
  padding-bottom: 55px; }
  .post-comments .title {
    font-size: 26px;
    margin-bottom: 40px;
    font-weight: 500;
    font-family: var(--font-body); }

.latest__comments {
  margin-top: -30px; }
  .latest__comments .comments-box {
    padding: 30px 0;
    position: relative; }
  .latest__comments .comments-text {
    overflow: hidden; }
  .latest__comments .comments-avatar {
    border-radius: 50%;
    float: left; }
    @media (max-width: 767px) {
      .latest__comments .comments-avatar {
        float: none;
        margin-bottom: 10px; } }
    @media only screen and (min-width: 576px) and (max-width: 767px) {
      .latest__comments .comments-avatar {
        float: left;
        margin-bottom: 0px; } }
    .latest__comments .comments-avatar img {
      border-radius: 50%;
      width: 80px !important;
      margin-right: 30px; }
  .latest__comments li:last-child .comments-box {
    padding-bottom: 0px; }
  .latest__comments li.children {
    margin-left: 100px; }
    @media (max-width: 767px) {
      .latest__comments li.children {
        margin-left: 0px; } }
    @media only screen and (min-width: 576px) and (max-width: 767px) {
      .latest__comments li.children {
        margin-left: 50px; } }
    .latest__comments li.children .reply {
      top: 30px; }
      @media (max-width: 767px) {
        .latest__comments li.children .reply {
          top: 120px; } }
  .latest__comments .avatar-name {
    margin-bottom: 15px;
    overflow: hidden;
    position: relative; }
    .latest__comments .avatar-name h5 {
      font-size: 18px;
      margin-bottom: 0px;
      font-weight: 500;
      font-family: var(--font-body);
      margin-bottom: 8px; }
    .latest__comments .avatar-name span {
      font-size: 15px;
      color: var(--color-default); }
  .latest__comments .reply {
    display: inline-block;
    font-weight: 400;
    -webkit-transition: 0.3s;
    -o-transition: 0.3s;
    transition: 0.3s;
    line-height: 1;
    position: absolute;
    right: 0;
    margin-top: 0;
    top: 18px;
    text-decoration: none;
    font-size: 12px;
    color: #92939E;
    padding: 9px 20px;
    background-color: #0B0B29;
    border-radius: 15px;
    -webkit-border-radius: 15px;
    -moz-border-radius: 15px;
    -ms-border-radius: 15px;
    -o-border-radius: 15px; }
    @media (max-width: 767px) {
      .latest__comments .reply {
        top: 90px; } }
    .latest__comments .reply i {
      margin-right: 5px; }
    .latest__comments .reply:hover {
      color: #0B0B29;
      background-color: var(--color-primary); }
  .latest__comments--2 p {
    font-size: 14px;
    line-height: 24px;
    color: var(--color-black); }

.comment-respond {
  background: #0B0B29;
  padding: 40px 70px;
  padding-top: 60px;
  border-radius: 10px;
  -webkit-border-radius: 20px;
  -moz-border-radius: 20px;
  -ms-border-radius: 20px;
  -o-border-radius: 20px;
  background-image: url(../img/form_bg.png);
  background-position: center center;
  background-size: cover;
  background-repeat: no-repeat;
  position: relative;
  margin-top: 50px;
  z-index: 1; }
.comment-respond:before {
  content: '';
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 1px;
  position: absolute;
  border-radius: 20px;
  -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: add, add;
  mask-composite: add, add;
  mask-composite: add, add;
  mask-composite: add, add;
  mask-composite: add, add;
  mask-composite: add, add;
  -webkit-mask-composite: source-out;
  mask-composite: exclude;
  background: linear-gradient(334deg, #6780d2 0%, #2f3b8d 100%);
  z-index: -1;
  opacity: .8;
}
.comment-form .them-btn {
  padding: 15px 35px;
}
.comment-form .them-btn svg {
  margin-left: 20px;
}
.comment-form .them-btn svg path {
  fill: #080b18;
}
  @media (max-width: 1199px) {
    .comment-respond {
      padding: 50px 40px; } }
  @media (max-width: 767px) {
    .comment-respond {
      padding: 30px 20px; } }
  .comment-respond .field-item {
    margin-bottom: 20px;
    position: relative; }
    .comment-respond .field-item span {
      position: absolute;
      top: 33%;
      left: 0;
      transform: translateY(-43%);
      opacity: 0.8; }
    .comment-respond .field-item.filed-textarea span {
      top: 11%; }
  .comment-respond .title {
    font-size: 26px;
    margin-bottom: 20px;
    font-weight: 500;
    font-family: var(--font-body); }
  .comment-respond p {
    margin-bottom: 28px; }
  .comment-respond .form .field-item > input, .comment-respond .form .field-item > textarea {
    height: 60px;
    margin-bottom: 20px;
    padding: 0 20px;
    width: 100%;
    font-size: 16px;
    color: var(--color-black);
    border-radius: 0;
    background-color: transparent;
    border-bottom: 2px solid #2C398C;
    font-weight: 400;
    color: var(--color-white);
    padding: 0;
    padding-left: 30px; }
    .comment-respond .form .field-item > input::-webkit-input-placeholder, .comment-respond .form .field-item > textarea::-webkit-input-placeholder {
      color: rgba(255, 255, 255, 0.5);
      opacity: 1; }
    .comment-respond .form .field-item > input::-moz-placeholder, .comment-respond .form .field-item > textarea::-moz-placeholder {
      color: rgba(255, 255, 255, 0.5);
      opacity: 1; }
    .comment-respond .form .field-item > input:-ms-input-placeholder, .comment-respond .form .field-item > textarea:-ms-input-placeholder {
      color: rgba(255, 255, 255, 0.5);
      opacity: 1; }
    .comment-respond .form .field-item > input:-moz-placeholder, .comment-respond .form .field-item > textarea:-moz-placeholder {
      color: rgba(255, 255, 255, 0.5);
      opacity: 1; }
    .comment-respond .form .field-item > input:focus, .comment-respond .form .field-item > textarea:focus {
      border-color: var(--color-primary); }
  .comment-respond__btn button {
    padding: 14px 35px; }

.blog-btn {
  background-color: var(--color-primary);
  padding: 11px 35px;
  color: #080B18;
  border-radius: 30px;
  -webkit-border-radius: 30px;
  -moz-border-radius: 30px;
  -ms-border-radius: 30px;
  -o-border-radius: 30px;
  font-size: 14px;
  font-weight: 700;
  text-transform: uppercase;
  display: inline-block; }
  .blog-btn:hover {
    color: #080B18; }

.sidebar-area {
  padding-left: 5px; }
  @media (max-width: 1199px) {
    .sidebar-area {
      padding-left: 0; } }

.contact-info__item {
  background-color: #1D1D3E;
  border-radius: 20px;
  -webkit-border-radius: 20px;
  -moz-border-radius: 20px;
  -ms-border-radius: 20px;
  -o-border-radius: 20px;
  padding: 60px 60px 55px;
  background-image: url(../img/shape/c_info_bg.jpg);
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center center;
  z-index: 1;
  position: relative; }
@media (max-width: 1199px) {
  .contact-info__item {
    padding: 50px 35px; } }
.contact-info__item .icon {
  width: 80px;
  height: 80px;
  border: 1px solid #333159;
  border-radius: 50%;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  -ms-border-radius: 50%;
  -o-border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 26px;
  background: linear-gradient(334deg, #6780d2 0%, #2f3b8d 100%);
  background-image: url(../img/shape/icon_bg.png);
  background-repeat: no-repeat;
  background-size: cover; }
.contact-info__item h3 {
  font-size: 20px;
  margin-bottom: 18px; }
.contact-info__item p {
  font-size: 24px;
  line-height: 34px;
  color: #C1C7DE;
  text-align: center;
  font-family: var(--font-heading); }
@media (max-width: 1199px) {
  .contact-info__item p {
    font-size: 21px;
    line-height: 32px; } }
.contact-info__item p a {
  color: currentColor; }
.contact-info__item a:hover {
  text-decoration: underline; }
.border_effect a {
  display: inline !important;
  width: 100%;
  background-repeat: no-repeat;
  background-position-y: -2px;
  background-image: linear-gradient(transparent calc(100% - 2px), currentColor 1px);
  transition: 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
  background-size: 0 100%;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden; }
.border_effect a:hover {
  background-size: 100% 100%;
  color: inherit; }

@media (max-width: 991px) {
  .tx-col-md-6 {
    width: 50%; } }
@media (max-width: 767px) {
  .tx-col-md-6 {
    width: 100%; } }

.xb-close {
  background: rgba(0, 0, 0, 0.04);
  border: 9px solid transparent;
  color: #777;
  width: 36px;
  height: 36px;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  position: relative; }
.xb-close::before, .xb-close::after {
  content: '';
  position: absolute;
  height: 2px;
  width: 100%;
  top: 50%;
  left: 0;
  margin-top: -1px;
  transform-origin: 50% 50%;
  -webkit-transition: all 0.25s cubic-bezier(0.645, 0.045, 0.355, 1);
  -khtml-transition: all 0.25s cubic-bezier(0.645, 0.045, 0.355, 1);
  -moz-transition: all 0.25s cubic-bezier(0.645, 0.045, 0.355, 1);
  -ms-transition: all 0.25s cubic-bezier(0.645, 0.045, 0.355, 1);
  -o-transition: all 0.25s cubic-bezier(0.645, 0.045, 0.355, 1);
  transition: all 0.25s cubic-bezier(0.645, 0.045, 0.355, 1);
  background-color: #1b1b1b; }
.xb-close::before {
  -webkit-transform: rotate(45deg);
  -khtml-transform: rotate(45deg);
  -moz-transform: rotate(45deg);
  -ms-transform: rotate(45deg);
  -o-transform: rotate(45deg);
  transform: rotate(45deg); }
.xb-close::after {
  -webkit-transform: rotate(-45deg);
  -khtml-transform: rotate(-45deg);
  -moz-transform: rotate(-45deg);
  -ms-transform: rotate(-45deg);
  -o-transform: rotate(-45deg);
  transform: rotate(-45deg); }
.xb-close:hover::before, .xb-close:hover::after {
  -webkit-transform: rotate(0);
  -khtml-transform: rotate(0);
  -moz-transform: rotate(0);
  -ms-transform: rotate(0);
  -o-transform: rotate(0);
  transform: rotate(0); }



.xb-header_btn a {
  font-family: var(--font-heading);
  line-height: 28px;
  letter-spacing: -0.16px;
  color: var(--color-primary);
  padding: 10px 20px;
  border: 1px solid var(--color-primary); }

.side-menu-4 {
  margin-left: 40px; }

.header-btn .login-btn {
  font-size: 16px;
  font-weight: 600;
  color: #ebf7fd;
  border: 1px solid #41445c;
  height: 50px;
  width: 102px;
  border-radius: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s; }
@media (max-width: 767px) {
  .header-btn .login-btn {
    display: none; } }
.header-btn .login-btn:hover {
  color: #080B18;
  background-color: var(--color-primary);
  border-color: var(--color-primary);
  -webkit-transform: translateY(-2px);
  transform: translateY(-2px); }
.header-btn .login-btn:hover i {
  color: #080B18; }
.header-btn .login-btn i {
  color: var(--color-primary);
  padding-right: 6px;
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s; }


/*----------------------------------------*/
/*  24. breadcrumb
/*----------------------------------------*/
.breadcrumb-container a {
  color: var(--color-default);
  font-weight: 500;
}
.breadcrumb-container a:hover {
  color: #fff;
}
.breadcrumb-container .current-item {
  color: #fff;
  font-width: 600;
}

/*----------------------------------------*/
/*  30. mobile-menu
/*----------------------------------------*/
.xb-hamburger-menu {
  display: none; }
@media (max-width: 991px) {
  .xb-hamburger-menu {
    display: block; } }

.xb-nav-mobile-button {
  position: relative;
  cursor: pointer;
  font-size: 25px;
  color: var(--color-dark); }
.xb-nav-mobile-button.btn-white {
  color: var(--color-white); }

.xb-header-menu-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 999;
  background-color: rgba(0, 0, 0, 0.5);
  -webkit-transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
  -khtml-transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
  -moz-transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
  -ms-transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
  -o-transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
  transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
  opacity: 0;
  visibility: hidden; }

.xb-header-menu {
  position: fixed;
  height: 100vh;
  width: 300px;
  top: 0;
  left: 0;
  bottom: 0;
  margin: 0;
  z-index: 1010;
  overflow: hidden;
  -webkit-transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
  -khtml-transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
  -moz-transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
  -ms-transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
  -o-transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
  transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
  -webkit-transform: translateX(-100%);
  -khtml-transform: translateX(-100%);
  -moz-transform: translateX(-100%);
  -ms-transform: translateX(-100%);
  -o-transform: translateX(-100%);
  transform: translateX(-100%);
  visibility: hidden;
  background-color: #000; }

.xb-header-menu.active {
  visibility: visible;
  opacity: 1;
  -webkit-transform: translateX(0%);
  -khtml-transform: translateX(0%);
  -moz-transform: translateX(0%);
  -ms-transform: translateX(0%);
  -o-transform: translateX(0%);
  transform: translateX(0%); }

.xb-header-menu.active + .xb-header-menu-backdrop {
  opacity: 1;
  visibility: visible; }

.xb-header-menu-scroll {
  padding: 50px 25px 40px; }

.xb-header-menu-scroll .xb-close {
  position: absolute;
  top: 0;
  right: 0; }

.xb-header-menu-scroll .xb-menu-primary > li {
  padding-left: 0;
  padding-right: 0; }

.xb-header-menu-scroll .xb-menu-primary > li > a .left-icon {
  margin-right: 4px; }

.xb-header-menu-scroll .xb-menu-primary > li .elementor-section {
  max-width: 100%; }

.xb-header-menu-scroll .xb-menu-primary > li .elementor-container {
  margin: 10px 0 !important; }

.xb-header-menu-scroll .xb-menu-primary > li .sub-menu.xb-mega-menu, .xb-header-menu-scroll .xb-menu-primary > li .children.xb-mega-menu {
  padding-left: 0; }

.xb-menu-toggle {
  position: absolute;
  top: 10px;
  height: 28px;
  line-height: 28px;
  text-align: center;
  right: 0;
  width: 28px;
  cursor: pointer;
  font-size: 15px;
  -webkit-transition: all 0.25s cubic-bezier(0.645, 0.045, 0.355, 1);
  -khtml-transition: all 0.25s cubic-bezier(0.645, 0.045, 0.355, 1);
  -moz-transition: all 0.25s cubic-bezier(0.645, 0.045, 0.355, 1);
  -ms-transition: all 0.25s cubic-bezier(0.645, 0.045, 0.355, 1);
  -o-transition: all 0.25s cubic-bezier(0.645, 0.045, 0.355, 1);
  transition: all 0.25s cubic-bezier(0.645, 0.045, 0.355, 1);
  color: #1b1b1b;
  -webkit-border-radius: 3px;
  -khtml-border-radius: 3px;
  -moz-border-radius: 3px;
  -ms-border-radius: 3px;
  -o-border-radius: 3px;
  border-radius: 3px;
  background-color: rgba(0, 0, 0, 0.03);
  display: inline-block; }

.xb-menu-toggle:before {
  content: "\f107";
  font-weight: 400;
  font-family: "Font Awesome 5 Pro"; }

.xb-menu-toggle.active:before {
  content: "\f106"; }

.xb-menu-primary {
  list-style: none;
  margin: 0;
  padding: 0; }

.xb-menu-primary li {
  position: relative; }

.xb-menu-primary li a {
  display: block;
  line-height: 46px;
  color: var(--color-white);
  font-size: 14px;
  font-weight: 600;
  border-bottom: 1px solid rgba(255, 255, 255, 0.06); }

.xb-menu-primary li > a:hover, .xb-menu-primary li > a.current, .xb-menu-primary li.current_page_item > a, .xb-menu-primary li.current-menu-item > a, .xb-menu-primary li.current_page_ancestor > a, .xb-menu-primary li.current-menu-ancestor > a {
  color: var(--color-primary); }

.xb-menu-primary .sub-menu, .xb-menu-primary .children {
  position: relative;
  display: none;
  list-style: none;
  margin: 0;
  padding: 0; }

.xb-menu-primary .sub-menu li a, .xb-menu-primary .children li a {
  font-weight: 500;
  padding-left: 14px; }

.xb-menu-primary > li > .sub-menu > li > .sub-menu > li a {
  padding-left: 28px; }

.xb-menu-primary .xb-megamenu .elementor-container > .elementor-column {
  width: 100%; }

.xb-menu-primary .xb-megamenu .elementor-container .elementor-widget-wrap {
  padding: 0 !important; }

.xb-menu-primary .xb-megamenu .xb-link li {
  margin: 0; }

.xb-menu-primary .xb-megamenu .xb-heading {
  padding-left: 14px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
  padding-top: 12px;
  padding-bottom: 12px; }

.xb-menu-primary .xb-megamenu .xb-heading .xb-item--title {
  margin-bottom: 0 !important;
  font-size: 15px !important;
  border-bottom: none !important; }

.xb-menu-primary .xb-megamenu .elementor-widget-heading > .elementor-widget-container {
  margin-bottom: 0 !important;
  border-bottom: none !important; }

.xb-menu-primary .xb-megamenu .elementor-section .elementor-container {
  flex-wrap: wrap; }

.xb-menu-primary .xb-menu-toggle:hover {
  color: var(--color-primary); }

.xb-logo-mobile {
  margin-bottom: 40px; }

.xb-logo-mobile img {
  height: 32px; }

.xb-header-mobile-search {
  margin-bottom: 20px; }

.xb-header-mobile-search form {
  position: relative; }

.xb-header-mobile-search .search-field {
  height: 50px;
  border: none;
  padding: 0 25px;
  border-radius: 4px;
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  -ms-border-radius: 4px;
  -o-border-radius: 4px;
  color: #fff;
  border: 2px solid rgba(150, 144, 162, 0.09);
  padding: 12px 15px 13px;
  background: transparent;
  font-size: 16px; }

.xb-header-mobile-search .search-field:focus {
  color: #fff;
  border-color: var(--color-primary); }

.xb-header-mobile-search .search-submit {
  position: absolute;
  top: 5px;
  right: 10px;
  -webkit-border-radius: 0px;
  -khtml-border-radius: 0px;
  -moz-border-radius: 0px;
  -ms-border-radius: 0px;
  -o-border-radius: 0px;
  border-radius: 0px;
  line-height: 36px;
  padding: 0;
  width: 30px;
  height: 40px;
  line-height: 40px;
  background: none;
  color: var(--primary-color); }

.xb-menu-close {
  background-color: rgba(0, 0, 0, 0.03);
  position: absolute;
  top: 0;
  right: 0;
  -webkit-border-radius: 0px;
  -khtml-border-radius: 0px;
  -moz-border-radius: 0px;
  -ms-border-radius: 0px;
  -o-border-radius: 0px;
  border-radius: 0px; }

.xb-header-wrap .xb-menu-toggle {
  color: var(--color-white);
  background-color: rgba(255, 255, 255, 0.03); }
.xb-header-wrap .xb-menu-close {
  background-color: rgba(255, 255, 255, 0.03); }
.xb-header-wrap .xb-close::before,
.xb-header-wrap .xb-close::after {
  background-color: #fff; }

/*----------------------------------------*/
/*  32. preloader
/*----------------------------------------*/
.preloader {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: #080B18;
  z-index: 800;
  height: 100%;
  width: 100%;
  display: table;
  transition: .2s; }

.loader {
  display: table-cell;
  text-align: center;
  vertical-align: middle; }

.line-scale > div:nth-child(1) {
  -webkit-animation: line-scale 1s -0.4s infinite cubic-bezier(0.2, 0.68, 0.18, 1.08);
  animation: line-scale 1s -0.4s infinite cubic-bezier(0.2, 0.68, 0.18, 1.08); }

.line-scale > div:nth-child(2) {
  -webkit-animation: line-scale 1s -0.3s infinite cubic-bezier(0.2, 0.68, 0.18, 1.08);
  animation: line-scale 1s -0.3s infinite cubic-bezier(0.2, 0.68, 0.18, 1.08); }

.line-scale > div:nth-child(3) {
  -webkit-animation: line-scale 1s -0.2s infinite cubic-bezier(0.2, 0.68, 0.18, 1.08);
  animation: line-scale 1s -0.2s infinite cubic-bezier(0.2, 0.68, 0.18, 1.08); }

.line-scale > div:nth-child(4) {
  -webkit-animation: line-scale 1s -0.1s infinite cubic-bezier(0.2, 0.68, 0.18, 1.08);
  animation: line-scale 1s -0.1s infinite cubic-bezier(0.2, 0.68, 0.18, 1.08); }

.line-scale > div:nth-child(5) {
  -webkit-animation: line-scale 1s 0s infinite cubic-bezier(0.2, 0.68, 0.18, 1.08);
  animation: line-scale 1s 0s infinite cubic-bezier(0.2, 0.68, 0.18, 1.08); }

.line-scale > div {
  background-color: var(--color-primary);
  width: 4px;
  height: 35px;
  border-radius: 2px;
  margin: 2px;
  -webkit-animation-fill-mode: both;
  animation-fill-mode: both;
  display: inline-block; }

@-webkit-keyframes line-scale {
  0% {
    -webkit-transform: scaley(1);
    transform: scaley(1); }
  50% {
    -webkit-transform: scaley(0.4);
    transform: scaley(0.4); }
  100% {
    -webkit-transform: scaley(1);
    transform: scaley(1); } }
@keyframes line-scale {
  0% {
    -webkit-transform: scaley(1);
    transform: scaley(1); }
  50% {
    -webkit-transform: scaley(0.4);
    transform: scaley(0.4); }
  100% {
    -webkit-transform: scaley(1);
    transform: scaley(1); } }
.xb-contact {
  position: relative;
  top: -110px;
  border-radius: 22px;
  overflow: hidden; }

.xb-inner {
  border-radius: 20px 0 0 20px;
  background: linear-gradient(335deg, #435fb2 0%, #111b5e 66.48%, #0c1449 100%);
  padding: 60px 70px 70px 70px;
  background-size: cover; }
@media (max-width: 767px) {
  .xb-inner {
    padding: 43px 25px 50px 25px; } }
.xb-inner .xb-item--title {
  font-size: 40px;
  line-height: 50px;
  letter-spacing: -0.04px;
  margin-bottom: 45px; }
@media (max-width: 767px) {
  .xb-inner .xb-item--title {
    font-size: 30px;
    line-height: 39px;
    letter-spacing: 0;
    margin-bottom: 33px; } }
.xb-inner .xb-item--field {
  position: relative;
  margin-bottom: 23px; }
.xb-inner .xb-item--field .icon:before {
  position: absolute;
  top: 19px;
  left: 0;
  width: 18px;
  height: 18px;
  content: "";
  background-repeat: no-repeat;
  z-index: 1;
  opacity: 0.8;
}
.xb-inner .xb-item--field .icon-user::before {
  background-image: url("../img/contact-user.svg");
}
.xb-inner .xb-item--field .icon-mail::before {
  background-image: url("../img/contact-email.svg");
}
.xb-inner .xb-item--field .icon-number::before {
  background-image: url("../img/contact-call.svg");
}
.xb-inner .xb-item--field .icon-message::before {
  background-image: url("../img/contact-massage.svg");
}
.xb-contact-form .xb-item--field .icon::before {
  left: 20px;
  top: 22px;
}
.xb-inner .xb-item--field input,
.xb-inner .xb-item--field textarea {
  background: none;
  border-bottom: 2px solid #2c398c;
  padding: 0;
  font-weight: 500;
  line-height: 24px;
  font-size: 16px;
  padding: 0 0 0 29px;
  height: 56px;
  line-height: 50px;
  color: var(--color-white); }
.xb-inner .xb-item--field input::placeholder,
.xb-inner .xb-item--field textarea::placeholder{
  color: var(--color-default);
  opacity: 0.5; }
.xb-inner .xb-item--checkbox {
  padding-left: 37px;
  padding-top: 30px; }
.xb-inner .xb-item--checkbox .form-check-label {
  color: #b0b3c7; }
.xb-inner .xb-item--checkbox .form-check-label a {
  color: #b0b3c7;
  text-decoration: underline; }
.xb-inner .xb-item--content {
  padding-left: 10px; }
.xb-inner .xb-item--content a {
  color: var(--color-default);
  text-decoration: underline; }
.xb-inner .xb-item--contact-btn {
  padding-top: 44px; }
.xb-inner .xb-item--contact-btn .them-btn {
  width: 100%;
  height: 50px; }

.form-check input {
  border: 2px solid #B0B3C7 !important;
  background-color: transparent !important;
  margin-top: 6px; }

.testimonial-wrap {
  border-radius: 0 20px 20px 0;
  background: linear-gradient(335deg, #435fb2 0%, #111b5e 66.48%, #0c1449 100%);
  padding: 70px 90px 57px 68px;
  margin-left: -1px;
  background-size: cover; }
@media (max-width: 767px) {
  .testimonial-wrap {
    padding: 30px; } }

.testimonial-slider {
  padding-bottom: 180px; }
@media (max-width: 1199px) {
  .testimonial-slider {
    padding-bottom: 70px; } }
.testimonial-slider .swiper-pagination .swiper-pagination-bullet {
  height: 10px;
  width: 10px;
  border-radius: 2px;
  background: var(--color-primary);
  opacity: 0.1; }
.testimonial-slider .swiper-pagination .swiper-pagination-bullet-active {
  opacity: 1; }

.xb-testimonial .xb-item--avater {
  margin-bottom: 80px; }
@media (max-width: 1199px) {
  .xb-testimonial .xb-item--avater {
    margin-bottom: 35px; } }
.xb-testimonial .xb-item--img {
  margin-right: 20px; }
.xb-testimonial .xb-item--img img {
  border-radius: 10px; }
.xb-testimonial .xb-item--holder {
  transform: translateY(6px); }
.xb-testimonial .xb-item--nationality span {
  font-size: 18px;
  font-weight: 500;
  line-height: 24px;
  padding-left: 10px; }
.xb-testimonial .xb-item--title {
  font-size: 22px;
  line-height: 34px;
  letter-spacing: -0.03px;
  padding-top: 8px; }
.xb-testimonial .xb-item--sub-title {
  font-family: var(--font-heading);
  line-height: 34px;
  letter-spacing: -0.03px;
  font-weight: 500; }
.xb-testimonial .xb-item--comment {
  font-size: 22px;
  line-height: 33px;
  color: var(--color-white); }

.xb-footer-bottom {
  background: #080b18;
  border-radius: 20px;
  padding: 60px 70px 32px;
  position: relative; }
@media (max-width: 767px) {
  .xb-footer-bottom {
    padding: 49px 50px 30px; } }
.xb-footer-bottom .footer-copyright {
  padding-top: 22px; }

.xb-footer-wrap {
  padding-bottom: 30px;
  align-items: flex-start;
  position: relative; }
.xb-footer-wrap::before {
  content: '';
  position: absolute;
  height: 1px;
  width: 100%;
  left: 0;
  bottom: 0;
  background: linear-gradient(136deg, #080b18 0%, #2dd98f 47.79%, #080b18 100%); }
.xb-footer-wrap .xb-item--footer_widget span {
  font-weight: 500;
  line-height: 30px; }
.xb-footer-wrap .xb-item--footer_widget-list {
  list-style: none;
  margin-top: 20px; }
.xb-footer-wrap .xb-item--footer_widget-list li a {
  font-size: 16px;
  font-weight: 600;
  line-height: 35px;
  color: var(--color-default); }
.xb-footer-wrap .xb-item--footer_widget-list li a:hover {
  color: #fff; }
.xb-footer-wrap .xb-item--footer_widget-list .underline {
  text-decoration: underline; }
.xb-footer-social li:not(:last-child) {
  margin-right: 6px; }
.xb-footer-social a {
  height: 40px;
  width: 40px;
  border-radius: 5px;
  background: #21232f;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-white);
  -webkit-transition: 0.4s;
  -o-transition: 0.4s;
  transition: 0.4s; }
.xb-footer-social a:hover {
  background: var(--color-primary);
  color: var(--color-black); }
.xb-item--footer_widget-community {
  max-width: 223px;
  border-radius: 5px;
  border: 1px solid #6780D2;
  text-align: center;
  padding: 42px 0;
  margin-top: 9px;
  background: linear-gradient(345deg, #435fb2 0%, #111b5e 66.48%, #0c1449 100%); }
.xb-item--footer_widget-community h3 {
  font-family: var(--font-body);
  font-size: 18px;
  line-height: 26px;
  letter-spacing: -0.03px;
  margin-bottom: 20px; }
.xb-item--footer_widget-community a {
  color: #080B18;
  line-height: 50px;
  letter-spacing: 0.10px;
  text-transform: uppercase;
  background: var(--color-primary);
  border-radius: 30px;
  padding: 0px 45px;
  font-weight: 700;
  font-size: 16px; }
.xb-item--footer_widget-community a:hover .btn_label {
  transform: translateY(-86%); }
.xb-item--footer_widget-community a .btn_icon {
  margin: 0; }
.xb-item--footer_widget-community a .btn_icon svg {
  margin-right: 10px;
}
.xb-item--footer_widget-community a .btn_label {
  line-height: 1;
  padding: 15px 0px 13px; }
.xb-item--footer_widget-community a img {
  margin-right: 10px; }

@media (max-width: 991px) {
  .footer-shape {
    display: none; } }
.footer-shape .shape {
  position: absolute; }
.footer-shape .shape--1 {
  left: 60px;
  bottom: 93px; }
.footer-shape .shape--2 {
  position: absolute;
  right: 20px;
  bottom: 18%; }
@media only screen and (min-width: 1200px) and (max-width: 1500px) {
  .footer-shape .shape--2 {
    bottom: 10%;
    max-width: 100px; } }
@media (max-width: 1199px) {
  .footer-shape .shape--2 {
    right: 15px;
    bottom: 11%;
    max-width: 80px; } }

.xb-contact-form {
  backdrop-filter: blur(4.8000001907px);
  background: rgba(74, 33, 239, 0.18);
  border: 1px solid rgba(255, 255, 255, 0.28);
  border-radius: 20px;
  padding: 20px; }
.xb-contact-form .xb-inner {
  border-radius: 20px;
  background: #0d0b33;
  padding: 52px 60px 60px 60px;
  position: relative;
  border: none;
  height: 100%;
  margin-right: 18px;
  z-index: 1; }
@media (max-width: 767px) {
  .xb-contact-form .xb-inner {
    padding: 40px 25px 40px 25px;
    margin-right: 0; } }
.xb-contact-form .xb-inner::before {
  content: '';
  top: -1px;
  left: -1px;
  right: -1px;
  bottom: -1px;
  padding: 1px;
  height: 100%;
  position: absolute;
  border-radius: 20px;
  -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: add, add;
  mask-composite: add, add;
  -webkit-mask-composite: source-out;
  mask-composite: exclude;
  background: linear-gradient(162deg, #8f7bff 0%, #413886 28.35%, #0f0d39 73.81%, #2dd98f 100%);
  z-index: -1; }
.xb-contact-form .xb-item--sub-title {
  font-size: 14px;
  font-weight: 600;
  line-height: 30px; }
.xb-contact-form .xb-item--title {
  font-size: 26px;
  line-height: 36px;
  margin-bottom: 10px;
  margin-top: 18px;
  margin-bottom: 20px;
  max-width: 401px; }
.xb-contact-form .xb-item--field {
  margin-bottom: 18px; }
.xb-contact-form .xb-item--field input,
.xb-contact-form .xb-item--field textarea {
  height: 60px;
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 10px;
  background: #151246;
  padding: 0 48px;
  color: var(--color-white); }
.xb-contact-form .xb-item--contact-btn {
  padding-top: 26px;
}
.xb-contact-form .xb-item--field input::placeholder,
.xb-contact-form .xb-item--field textarea::placeholder {
  color: var(--color-white);
  opacity: .5; }
.xb-contact-form .xb-item--field textarea {
  height: 152px;
  padding: 5px 48px;
}
.xb-contact-form .xb-item--text-msg {
  position: relative; }
.xb-contact-form .xb-item--text-msg span {
  position: absolute;
  top: 10px;
  left: 30px; }
.xb-contact-form .xb-item--massage {
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 10px;
  background: #151246;
  padding: 0 48px;
  height: 152px;
  padding-top: 10px;
  color: var(--color-white); }
.xb-contact-form .xb-item--massage::-webkit-input-placeholder {
  color: #888686;
  opacity: 1; }
.xb-contact-form .xb-item--massage::-moz-placeholder {
  color: #888686;
  opacity: 1; }
.xb-contact-form .xb-item--massage:-ms-input-placeholder {
  color: #888686;
  opacity: 1; }
.xb-contact-form .xb-item--massage:-moz-placeholder {
  color: #888686;
  opacity: 1; }
.xb-contact-form .xb-item--massage::placeholder {
  color: var(--color-white);
  opacity: .5; }
.xb-contact-form .xb-item--contact-btn .them-btn {
  width: 315px;
  height: 70px;
  padding: 0; }
@media (max-width: 767px) {
  .xb-contact-form .xb-item--contact-btn .them-btn {
    width: 100%; } }

.footer-apps {
  background: #0d0b33;
  border-radius: 20px;
  position: relative;
  padding: 13px 29px 0 28px;
  z-index: 1; }
.footer-apps::before {
  content: '';
  top: -1px;
  left: -1px;
  right: -1px;
  bottom: -1px;
  padding: 1px;
  height: 100%;
  position: absolute;
  border-radius: 20px;
  -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: add, add;
  mask-composite: add, add;
  -webkit-mask-composite: source-out;
  mask-composite: exclude;
  background: linear-gradient(334deg, #6780d2 0%, #2f3b8d 100%);
  z-index: -1; }
.footer-apps .xb-item--img {
  height: 360px;
  overflow: hidden; }
.footer-apps .xb-item--shadow {
  position: absolute;
  left: 0;
  right: 0;
  bottom: -82px; }
.footer-apps .xb-item--holder {
  padding: 11px 30px 62px  63px; }
@media (max-width: 1199px) {
  .footer-apps .xb-item--holder {
    padding: 30px 0; } }
.footer-apps .xb-item--title {
  text-transform: capitalize; }
.footer-apps .xb-item--button {
  display: flex;
  padding-top: 33px; }
.footer-apps .xb-item--button .apps-btn {
  height: 50px;
  width: 162px;
  margin-right: 20px;
  padding: 0; }
@media (max-width: 767px) {
  .footer-apps .xb-item--button .apps-btn {
    font-size: 15px; } }
.footer-apps .xb-item--button .apps-btn span {
  margin-right: 10px;
  display: flex;
  align-items: center; }

.xb-contact_info .contact-method {
  display: flex;
  align-items: center;
  font-size: 24px;
  line-height: 34px;
  font-weight: 500;
  color: var(--color-white); }
.xb-contact_info .contact-method span {
  height: 56px;
  width: 56px;
  border-radius: 100%;
  background: #151246;
  margin-right: 20px;
  display: flex;
  align-items: center;
  justify-content: center; }

.xb-footer-widget {
  border-top: 1px solid #2a284b;
  border-bottom: 1px solid #2a284b;
  padding-bottom: 30px;
}
.xb-footer-widget .xb-item--footer_nav {
  margin-top: -10px; }
@media (max-width: 767px) {
  .xb-footer-widget .xb-item--footer_nav {
    justify-content: center; } }
.xb-footer-widget .xb-item--footer_nav li {
  margin-right: 40px;
  margin-top: 10px; }
.xb-footer-widget .xb-item--footer_nav li a {
  font-size: 18px;
  font-weight: 500;
  color: var(--color-white); }
.xb-footer-widget .xb-item--footer_nav li a:hover {
  text-decoration: underline; }
.footer-newsletter {
  position: relative;
}
@media (max-width: 991px) {
  .footer-newsletter {
    margin-top: 30px; } }
@media (max-width: 767px) {
  .footer-newsletter {
    width: 100%; } }
.footer-newsletter::before {
  content: '';
  position: absolute;
  height: 120px;
  width: 1px;
  background: #2a284b;
  left: 0;
  top: -30px;
  margin-left: -159px; }
.footer-newsletter input {
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 30px;
  width: 485px;
  height: 60px;
  background: #151246;
  padding-left: 54px;
  color: var(--color-white); }
@media (max-width: 767px) {
  .footer-newsletter input {
    width: 100%; } }
.footer-newsletter button {
  position: absolute;
  top: 50%;
  right: 0;
  transform: translateY(-50%);
  line-height: 50px;
  height: 40px;
  width: 133px;
  border-radius: 30px;
  background: #0d0b33;
  color: var(--color-white);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px; }
.footer-newsletter button:hover {
  background-color: var(--color-primary);
  color: #080b18; }

@media (max-width: 767px) {
  .footer-copyright {
    justify-content: center;
    text-align: center; } }
@media (max-width: 767px) {
  .footer-copyright .footer-link {
    margin-top: 20px; } }
.footer-copyright .footer-link li {
  margin-right: 11px; }
.footer-copyright .footer-link li a {
  color: var(--color-white);
  height: 40px;
  width: 40px;
  border-radius: 50px;
  background: #151246;
  display: flex;
  align-items: center;
  justify-content: center;
  -webkit-transition: 0.4s;
  -o-transition: 0.4s;
  transition: 0.4s; }
.footer-copyright .footer-link li a:hover {
  background: var(--color-primary);
  color: #080b18; }

.second-footer {
  padding-top: 409px;
  z-index: 1; }
@media (max-width: 991px) {
  .second-footer {
    padding-top: 250px; } }
@media (max-width: 767px) {
  .second-footer {
    padding-top: 180px; } }

.footer-bg {
  position: absolute;
  height: 100%;
  width: 100%;
  top: 0;
  left: 0;
  z-index: -1; }

@media (max-width: 991px) {
  .xb-round_ranner {
    top: 9%; } }
@media (max-width: 767px) {
  .xb-round_ranner {
    top: 6%; } }
.xb-round_ranner .spin-img {
  animation: spin 20s infinite linear;
  -webkit-animation: spin 20s infinite linear; }
.xb-round_ranner span {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  height: 112px;
  width: 112px;
  background: linear-gradient(152deg, #8b6fff 0%, #0d0b33 51%, #2dd98f 100%);
  border-radius: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #623bfd; }

.ft-shape .shape {
  position: absolute;
  z-index: -1; }
.ft-shape .shape--1 {
  left: 7%;
  top: 44%; }
.ft-shape .shape--2 {
  left: 5.3%;
  top: 40%; }
.ft-shape .shape--3 {
  right: 5.3%;
  top: 38%; }
.ft-shape .shape--4 {
  right: 7%;
  top: 43%; }

.footer-line .line {
  position: absolute; }
.footer-line .line--1 {
  left: 4%;
  bottom: 21%; }
.footer-line .line--2 {
  right: 4%;
  bottom: 25%; }
.grd-border {
  position: relative;
  z-index: 1;
}
.grd-border::before {
  content: '';
  top: -1px;
  left: -1px;
  right: -1px;
  bottom: -1px;
  padding: 1px;
  position: absolute;
  border-radius: 20px;
  -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: add, add;
  mask-composite: add, add;
  mask-composite: add, add;
  -webkit-mask-composite: source-out;
  mask-composite: exclude;
  background: linear-gradient(162deg, #8f7bff 0%, #413886 28.35%, #0f0d39 73.81%, #2dd98f 100%);
  z-index: -1;
}
.google-map__inner iframe {
  height: 100%;
  width: 100%;
  -webkit-filter: invert(1);
  filter: invert(1);
}

/*# sourceMappingURL=main.css.map */
