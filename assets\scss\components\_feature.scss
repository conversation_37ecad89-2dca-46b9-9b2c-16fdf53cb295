/*----------------------------------------*/
/*  07. feature
/*----------------------------------------*/
.feature {
    &__wrap {
        margin-top: 80px;
        padding-top: 50px;
        border-top: 1px solid rgba(255, 255, 255, 0.11);
    }
    &__item {
        position: relative;
        .number {
            width: 64px;
            height: 64px;
            @include border-radius(50%);
            border: 1px solid #2D374A;
            font-weight: 500;
            font-size: 20px;
            margin: 0 auto 25px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        h3 {
            font-size: 20px;
            color: var(--color-white);
            margin-bottom: 16px;
        }
        p {
            font-size: 14px;
            color: #96A1B1;
            line-height: 23px;
        }
    }
    &__dot {
        position: absolute;
        top: 17px;
        right: -34px;
        span {
            display: inline-block;
            width: 8px;
            height: 8px;
            background-color: #fff;
            @include border-radius(50%);
            opacity: .2;
            &:not(:last-child) {
                margin-right: 5px;
            }
            &.active {
                opacity: 1;
            }
        }
    }
    &__wrapper {
        background-color: #F8F8F8;
        @include border-radius(15px);
        padding: 80px;
        margin-top: -145px;
        position: relative;
        z-index: 2;
        @include respond(lg) {
            padding: 40px;
        }
        @include respond(xs) {
            padding: 25px;
        }
    }
    &__single {
        padding: 60px;
        padding-left: 70px;
        padding-right: 50px;
        @include border-radius(15px);
        background: #FFF;
        box-shadow: 0px 1px 4px 0px rgba(0, 20, 90, 0.10);
        @include respond(laptop) {
            padding: 50px 40px;
        }
        @include respond(lg) {
            padding: 50px 40px;
        }
        @include respond(xs) {
            padding: 30px 25px;
        }
        h3 {
            margin-bottom: 40px;
        }
        a {
            font-size: 16px;
            color: #202026;
            display: flex;
            align-items: center;
            i {
                margin-right: 10px;
                width: 21px;
                height: 21px;
                @include border-radius(50%);
                color: #fff;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 12px;
            }
        }
    }
    &__list {
        li {
            font-size: 14px;
            color: #92969E;
            padding-left: 36px;
            display: flex;
            align-items: center;
            position: relative;
            &:not(:last-child) {
                margin-bottom: 25px;
            }
            i {
                position: absolute;
                left: 0;
                top: 4px;
                width: 24px;
                height: 24px;
                font-size: 10px;
                color: #202026;
                @include border-radius(50%);
                overflow: hidden;
                display: flex;
                align-items: center;
                justify-content: center;
                margin-right: 10px;
                z-index: 1;
                &::after {
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    content: "";
                    background: linear-gradient(90deg, var(--gradient-color-from) 0%, var(--gradient-color-to) 100%);
                    opacity: 0.10000000149011612;
                    z-index: -1;
                }
            }
        }
    }
    &__content-list {
        border-left: 1px solid #E9E9E9;
        li {
            max-width: 428px;
            padding: 30px;
            padding-left: 38px;
            @include transition(.3s);
            @include border-radius(10px);
            position: relative;
            &::before {
                position: absolute;
                top: 50%;
                left: -2px;
                @include transform(translateY(-50%));
                width: 4px;
                height: 0;
                content: "";
                @include border-radius(10px);
                @include transition(.3s);
                background: linear-gradient(19deg, var(--gradient-color-from) 0%, var(--gradient-color-to) 100%);
            }
            &:not(:last-child) {
                margin-bottom: 10px;
            }
            &:hover {
                background-color: #F8F8F8;
                &::before {
                    height: 124px;
                }
                h3 {
                    color: #020014;
                }
                p {
                    color: #6B6B71;
                }
            }
            h3 {
                font-size: 26px;
                color: #676672;
                margin-bottom: 20px;
                @include transition(.3s);
            }
            p {
                color: #A6A6AA;
                @include transition(.3s);
            }
        }
    }
    &__img {
        position: absolute;
        bottom: -7px;
        right: 6.5%;
        z-index: 1;
        @include respond(laptop) {
            right: -36px;
            max-width: 700px;
        }
        @include respond(lg) {
            max-width: 632px;
            right: -58px;
        }
        @include respond(md) {
            position: relative;
            margin-top: -10%;
        }
        @include respond(xs) {
            margin-top: -23%;
        }
        &::before {
            position: absolute;
            top: 11%;
            left: 0;
            width: 560px;
            height: 560px;
            @include border-radius(50%);
            opacity: 0.4000000059604645;
            content: "";
            z-index: -1;
            @include respond(xs) {
                top: 3%;
                left: -31px;
                width: 300px;
                height: 300px;
            }
        }
    }
    &__img-ss {
        .ss {
            position: absolute;
            &--1 {
                top: 18%;
                left: -05%;
                @include respond(xs) {
                    top: 12%;
                    max-width: 150px;
                }
            }
            &--2 {
                top: 31%;
                right: 19%;
                @include respond(xs) {
                    max-width: 200px;
                }
            }
            &--3 {
                top: 48%;
                left: -5%;
                @include respond(xs) {
                    max-width: 200px;
                }
            }
        }
    }
    &__img-shape {
        .shape {
            position: absolute;
            &--1 {
                bottom: 6%;
                left: 7%;
                @include respond(lg) {
                    left: 10%;
                    max-width: 100px;
                }
                @include respond(xs) {
                    left: -14px;
                    max-width: 70px;
                }
            }
            &--2 {
                bottom: 9%;
                right: -6%;
            }
        }
    }
    &__shape {
        .shape {
            position: absolute;
            &--1 {
                bottom: 0;
                left: 0;
            }
            &--2 {
                bottom: 30px;
                right: 0;
            }
        }
    }
}
.crm-feature {
    &__item {
        background-color: #0F0E1E;
        padding: 60px 40px;
        padding-top: 63px;
        @include border-radius(15px);
        border: 1px solid #1B1F2F;
        height: 100%;
        position: relative;
        &::before {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            border-radius: 15px;
            background-image: linear-gradient(120deg, #331C9C 0%, #9066FB 100%);
            content: "";
            @include transition(.3s);
            z-index: -1;
        }
        &:hover::before {
            transform: rotate(3.374deg);
        }
        h3 {
            font-size: 22px;
            color: #fff;
            margin-bottom: 30px;
            letter-spacing: -1px;
        }
        p {
            font-size: 15px;
            color: #8F9BB7;
            line-height: 23px;
        }
        .ft_icon {
            position: absolute;
            top: 50%;
            right: 42px;
            @include transform(translateY(-50%));
            @include respond(xs) {
                display: none;
            }
        }
    }
    &__title {
        font-size: 22px;
        background-image: linear-gradient(0deg, rgba(42,35,85,1) 0%, rgba(255,255,255,1) 70%);
    }
    &__shape {
        .shape {
            position: absolute;
            z-index: -1;
            &--1 {
                bottom: 25%;
                left: 0;
            }
            &--2 {
                bottom: 44%;
                right: 0;
            }
        }
    }
}

[class^="col-"]:last-child .feature__item .feature__dot {
    display: none;
}
@include respond(md) {
    [class^="col-"]:nth-child(2) .feature__item .feature__dot {
        display: none;
    }
}