/*----------------------------------------*/
/*  20. breadcrumb
/*----------------------------------------*/
.breadcrumb {
	min-height: 326px;
	padding: 120px 0;
	margin: 0;
    display: flex;
    align-items: center;
    @include respond(md) {
        min-height: 200px;
        padding: 80px 0;
    }
    &__title {
        text-align: center;
        color: #fff;
        font-size: 55px;
        @include respond(md) {
            font-size: 46px;
        }
        @include respond(xs) {
            font-size: 32px;
        }
    }
    &__shape {
        @include respond(xs) {
            display: none;
        }
        .shape {
            position: absolute;
            top: 50%;
            @include transform(translateY(-50%));
            &--1 {
                top: 53%;
                left: 0;
            }
            &--2 {
                right: 0;
            }
        }
    }
}