/*----------------------------------------*/
/*  09. sidebar
/*----------------------------------------*/
.sidebar-info {
    .sidebar-logo {
        @include transition(.3s);
        @include transform(translateY(30px));
        opacity: 0;
    }
    .sidebar-content {
        @include transition(.3s);
        @include transform(translateY(30px));
        opacity: 0;
    }
    .sidebar-menu {
        @include transition(.3s);
        @include transform(translateY(30px));
        opacity: 0;
    }
    .ct-content-wrap {
        @include transition(.3s);
        @include transform(translateY(30px));
        opacity: 0;
    }
    .sidebar-socials-wrap {
        @include transition(.3s);
        @include transform(translateY(30px));
        opacity: 0;
    }
}

.slide-bar.show {
    .sidebar-info {
        .sidebar-logo {
            @include transform(translateY(0));
            transition-delay: .2s;
            opacity: 1;
        }
        .sidebar-content {
            @include transform(translateY(0));
            transition-delay: .3s;
            opacity: 1;
        }
        .sidebar-menu {
            @include transform(translateY(0));
            transition-delay: .4s;
            opacity: 1;
        }
        .ct-content-wrap {
            @include transform(translateY(0));
            transition-delay: .5s;
            opacity: 1;  
        }
        .sidebar-socials-wrap {
            @include transform(translateY(0));
            transition-delay: .6s;
            opacity: 1;  
        }
    }
    
}

.sidebar-content {
    max-width: 360px;
    margin-bottom: 65px;
}
.sidebar-menu {
	li {
        &:not(:last-child) {
            margin-bottom: 30px;
        }
        a {
            font-size: 36px;
            text-transform: capitalize;
            color: var(--color-heading);
            &:hover {
                color: #000;
            }
        }
    }
}

.ct-content-wrap {
    color: #9F9F9F;
    .ct-title {
        margin-right: 30px;
    }
    .ct-item {
        padding-left: 30px;
        padding-right: 30px;
        position: relative;
        &+&::before {
            content: "";
            position: absolute;
            left: 0;
            top: 50%;
            margin-top: -16px;
            height: 33px;
            width: 1px;
            background-color: #9f9f9f;
        }
        a {
            color: inherit;
        }
        .tel {
            font-size: 24px;
            line-height: 30px;
        }
    }
}

@media(max-width:1199px) {
     .ct-content-wrap .qc-title {
     margin-right:25px
    }
   }
   @media(min-width:768px) and (max-width:991px) {
     .ct-content-wrap .qc-title {
     display:none
    }
   }
   @media(max-width:575px) {
     .ct-content-wrap .qc-title {
     display:none
    }
   }
    .ct-content-wrap .ct-item-wrap {
    margin-left:-30px;
    margin-right:-30px
   }
   @media(max-width:1199px) {
     .ct-content-wrap .ct-item-wrap {
     margin-left:-25px;
     margin-right:-25px
    }
   }
   @media(max-width:575px) {
     .ct-content-wrap .ct-item-wrap {
     justify-content:center
    }
   }
    .ct-content-wrap .ct-item {
    padding-left:30px;
    padding-right:30px;
    position:relative
   }
   @media(max-width:1199px) {
     .ct-content-wrap .ct-item {
     padding-left:25px;
     padding-right:25px
    }
   }
   .ct-content-wrap .ct-item+.ct-item:before {
    content:"";
    position:absolute;
    left:0;
    top:50%;
    margin-top:-16px;
    height:33px;
    width:1px;
    background-color:#9f9f9f
   }
   @media(min-width:768px) and (max-width:991px) {
    .ct-content-wrap .ct-item+.ct-item:before {
     display:none
    }
   }
   @media(max-width:480px) {
    .ct-content-wrap .ct-item+.ct-item:before {
     display:none
    }
   }
    .ct-content-wrap .ct-item .pxl-icon {
    margin-right:8px
   }
    .ct-content-wrap .ct-item a {
    color:inherit
   }
    .ct-content-wrap .ct-item a:hover {
    color:inherit
   }
    .ct-content-wrap .ct-item span a+a {
    display:block
   }
    .ct-content-wrap .ct-item .tel {
    font-size:24px;
    line-height:30px;
    &:hover {
        color: var(--color-heading);
    }
   }
   @media(max-width:1199px) {
     .ct-content-wrap .ct-item .tel {
     font-size:20px
    }
   }
   .ct-content-wrap .ct-item {
    flex-wrap:nowrap
   }

.sidebar-socials-wrap {
    a {
        color: var(--color-default);
        font-size: 14px;
        text-transform: uppercase;
        letter-spacing: .05em;
        position: relative;
        &:hover {
            color: var(--color-heading);
        }
        &:before {
            position: absolute;
            width: 100%;
            height: 1px;
            background: currentColor;
            top: 100%;
            left: 0;
            pointer-events: none;
            content: "";
            transform-origin: 100% 50%;
            transform: scale3d(0, 1, 1);
            transition: transform 0.3s;
        }
        &:hover::before {
            transform-origin: 0% 50%;
            transform: scale3d(1, 1, 1);
        }
        &:not(:last-child) {
            margin-right: 25px;
        }
    }
}