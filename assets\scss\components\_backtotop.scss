/*----------------------------------------*/
/*  13. backtotop
/*----------------------------------------*/

.progress-wrap {
  position: fixed;
  right: 30px;
  bottom: 40px;
  height: 35px;
  width: 35px;
  cursor: pointer;
  display: block;
  border-radius: 50px;
  box-shadow: inset 0 0 0 2px rgba(255, 170, 0, 0.08);
  z-index: 99;
  opacity: 0;
  visibility: hidden;
  -webkit-transition: all 200ms linear;
  transition: all 200ms linear;
  transform: translateY(15px);
  -webkit-transform: translateY(15px);
  -moz-transform: translateY(15px);
  -ms-transform: translateY(15px);
  -o-transform: translateY(15px);
}

.progress-wrap::after {
  position: absolute;
  content: '\f176';
  font-family: "Font Awesome 5 Pro";
  text-align: center;
  line-height: 35px;
  font-size: 15px;
  color: var(--color-primary);
  left: 0;
  top: 0;
  height: 35px;
  width: 35px;
  cursor: pointer;
  display: block;
  font-weight: 700;
  z-index: 1;
  -webkit-transition: all 200ms linear;
  transition: all 200ms linear;
  font-size: 13px;
}

.progress-wrap svg path {
  fill: none;
}

.progress-wrap svg.progress-circle path {
  stroke: var(--color-primary);
  stroke-width: 4;
  box-sizing: border-box;
  -webkit-transition: all 200ms linear;
  transition: all 200ms linear;
}

.progress-wrap.active-progress {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}
.progress-wrap.style-2::after {
  color: var(--color-primary-2);
}
.progress-wrap.style-2 svg.progress-circle path {
  stroke: var(--color-primary-2);
}