/* Navigation css */
.main-menu {
    display: flex;
    align-items: center;
    flex-grow: 1;
    ul {
        @include d-flex;
        list-style: none;
        padding: 0;
        margin: 0;
        li {
            position: relative;
            &:not(:last-child) {
                margin-right: 48px;
                @include respond(laptop) {
                    margin-right: 32px;
                }
                @include respond(lg) {
                    margin-right: 20px;
                }
            }
            .submenu li {
                margin-right: 0;
            }
            a {
                display: inline-block;
                text-decoration: none;
                font-size: 16px;
                font-weight: 500;
                color: var(--color-dark);
                padding: 35px 0;
                position: relative;
                line-height: 22px;
                @include respond(lg) {
                    font-size: 15px;
                }
            }
            &.menu-item-has-children {
                & > a::after {
                    content: "\f078";
                    transform: translateY(-1px);
                    display: inline-block;
                    padding-left: 4px;
                    font-family: "Font Awesome 5 Pro";
                    font-weight: 900;
                    font-size: 10px;
                }
                &:hover > .submenu {
                    opacity: 1;
                    visibility: visible;
                    @include transform(none !important);
                    pointer-events: all;
                }
            }
            .submenu {
                -webkit-box-orient: vertical;
                -webkit-box-direction: normal;
                    -ms-flex-direction: column;
                        flex-direction: column;
                position: absolute;
                min-width: 240px;
                top: 100%;
                opacity: 0;
                visibility: hidden;
                background: var(--color-white);
                left: -30px;
                padding: 20px 0;
                @include transition(.3s);
                z-index: 3;
                text-align: left;
                @include transform(translate3d(0,18px,0));
                pointer-events: none;
                box-shadow: 0 0.5rem 1.875rem rgba(0, 0, 0, 0.10);
                li {
                    a {
                        color: var(--color-black);
                        padding: 8px 30px;
                        display: block;
                        margin: 0;
                        font-size: 16px;
                    }
                }
                ul {
                    left: 100%;
                    top: 0px;
                }
            }
        }
    }
}

.main-menu ul li.menu-last ul.submenu {
	right: 0;
	left: auto;
}
.main-menu ul li.menu-last ul.submenu ul {
    right: auto;
    left: -100%;
}


.main-menu ul li ul.submenu .menu-item-has-children > a::after {
	position: absolute;
    top: 9px;
    right: 15px;
    content: "\f105";
    font-size: 13px;
}
