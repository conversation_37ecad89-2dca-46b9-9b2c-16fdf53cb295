/*----------------------------------------*/
/*  08. service
/*----------------------------------------*/
.service {
    position: relative;
    z-index: 1;
    &__bg {
        background-repeat: no-repeat;
        background-size: cover;
    }
    &__item {
        padding: 35px 45px;
        display: flex;
        background: rgba(255, 255, 255, 0.06);
        @include border-radius(20px);
        @include transition(.3s);
        position: relative;
        @include respond(laptop) {
            padding: 35px 30px;
        }
        @include respond(lg) {
            padding: 30px 20px;
        }

        &::before {
            content: "";
            position: absolute;
            z-index: -1;
            top: -1px;
            left: -1px;
            right: -1px;
            bottom: -1px;
            border-radius: inherit;
            padding: 1px;
            -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
            mask-composite: add, add;
            -webkit-mask-composite: source-out;
            mask-composite: exclude;
            background-image: linear-gradient(177.79deg, rgba(255, 255, 255, 0.16) 0.25%, rgba(255, 255, 255, 0) 98.2%);
        }
        &:hover {
            background-color: #081322;
        }
        .icon {
            width: 64px;
            height: 64px;
            @include border-radius(50%);
            background: radial-gradient(50% 50% at 50% 50%, rgba(29, 39, 53, 0.1) 0%, rgba(145, 164, 191, 0.1) 100%);
            backdrop-filter: blur(25px);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 36px;
            position: relative;
            @include respond(lg) {
                width: 60px;
                height: 60px;
                margin-right: 20px;
            }
            &::before {
                content: "";
                position: absolute;
                z-index: -1;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                border-radius: inherit;
                padding: 1px;
                -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
                mask-composite: add, add;
                -webkit-mask-composite: source-out;
                mask-composite: exclude;
                background-image: linear-gradient(180deg, rgba(255, 255, 255, 0.12) 0%, rgba(255, 255, 255, 0) 58.59%);


            }
        }
        .content {
            width: calc(100% - 100px);
            h3 {
                font-size: 24px;
                margin-bottom: 15px;
                @include respond(lg) {
                    font-size: 19px;
                }
                a {
                    color: var(--color-white);
                }
            }
            p {
                font-size: 14px;
                font-weight: 400;
                color: #96A1B1;
                line-height: 23px;
                margin-bottom: 20px;
            }
            .link {
                color: var(--color-white);
                @include respond(lg) {
                    font-size: 15px;
                }
                i {
                    margin-left: 30px;
                    font-size: 16px;
                    @include transform(translateY(2px));
                    @include respond(lg) {
                        margin-left: 11px;
                    }
                }
            }
            
        }
        .service-link {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            display: inline-block;
        }
    }
}



